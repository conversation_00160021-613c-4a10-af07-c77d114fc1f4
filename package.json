{"name": "beco-careroutes-v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@analytics/google-analytics": "^1.1.0", "analytics": "^0.8.16", "becomap": "^1.5.69", "bootstrap": "^5.3.3", "framer-motion": "^11.11.11", "intro.js": "^7.2.0", "libphonenumber-js": "^1.11.16", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "prop-types": "^15.8.1", "react": "^18.3.1", "react-bootstrap": "^2.10.7", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-select": "^5.9.0", "react-sortablejs": "^6.1.4", "react-toastify": "^10.0.6", "react-window": "^1.8.11", "slick-carousel": "^1.8.1", "styled-components": "^6.1.13"}, "devDependencies": {"@eslint/js": "^9.13.0", "@iconify/react": "^5.2.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "terser": "^5.37.0", "vite": "^5.4.10"}}