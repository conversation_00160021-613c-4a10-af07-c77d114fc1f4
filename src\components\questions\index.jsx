import CustomRadioButton from '../../components/customradio';
import { useNavigate } from 'react-router-dom';
import { useMap } from '../../context/MapContext';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  Answers,
  ButtonGroup,
  LoaderImage,
  LoaderWrapper,
  Question,
  QuestionNairBody,
  QuestionNairFooter,
  QuestionNairWrapper,
  SingleAnswer,
  SingleAnswerInput,
  SkipButton,
} from '../../modules/MapView/index.styled';
import Button from '@/components/shared/button/button';
import Stepper from '@/components/stepper';

const Questions = ({
  questions,
  question,
  totalQuestions,
  currentStep,
  setCurrentStep,
  selectedOptions,
  setSelectedOptions,
  setShowQuestions,
}) => {
  const { sessionId, getEventSuggestions } = useMap();
  const [loading, setLoading] = useState(0)
  const handleChange = (option) => {
    setSelectedOptions((prevState) => {
      const prevAnswers = prevState[question.id] || [];

      if (question.type === 'MCQ_MULTI_ANSWER') {
        // Toggle selection for checkboxes
        const updatedAnswers = prevAnswers.includes(option)
          ? prevAnswers.filter((ans) => ans !== option) // Remove if already selected
          : [...prevAnswers, option]; // Add new selection

        return {
          ...prevState,
          [question.id]: updatedAnswers,
        };
      } else {
        // Single choice (radio button)
        return {
          ...prevState,
          [question.id]: [option], // Store as array to match API format
        };
      }
    });
  };

  useEffect(() => {
    console.log('selectedOptions: ' + JSON.stringify(selectedOptions));
  }, [selectedOptions]);

  const nextStep = () => {
    if (currentStep < totalQuestions - 1) {
      setCurrentStep(currentStep + 1);
    }
  };
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async() => {
    // const sessionId = '67b83903956d281dba33cc52'; // Example sessionId

    // Format selectedOptions to match BCAnswer[]
    const answers = Object.entries(selectedOptions).map(([questionId, answers]) => ({
      questionId,
      answers,
    }));

    console.log('Submitting answers:', answers);
    setLoading(1)
    await getEventSuggestions(sessionId, answers);
    setLoading(0)
    setShowQuestions(false);
    setCurrentStep(0);
  };

  if(loading){
    return (
      <LoaderWrapper>
      <LoaderImage src="/loader.gif"/>
      </LoaderWrapper>
    )
  }

  return (
    <QuestionNairWrapper>
      {/* <QuestionNairHeader>Let's Learn about your taste!</QuestionNairHeader> */}
      <QuestionNairBody>
        <motion.div
          key={question?.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'center',
          }}
        >
          <div className='hello' style={{
             display: 'flex',
             flexDirection: 'column',
             alignItems: 'flex-start',
             justifyContent: 'flex-start',
             width: '100%',
             borderRadius: '12px',
             boxShadow: '0px 10px 24px 0px rgba(0, 6, 36, .08)',
             backgroundColor: '#fff',
             padding: '30px 14px',
             position: 'relative',
             zIndex: 1,
          }}>

      <Stepper step={currentStep + 1} totalQuestions={questions.length} />
          <Question>{question?.question}</Question>

          <Answers>
            {question?.options.map((option, index) => (
              <motion.div
                key={option}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                style={{ margin: 0, padding: 0 }}
              >
                {question.type === 'MCQ_MULTI_ANSWER' ? (
                  // 'MCQ_MULTI_ANSWER'
                  // Render checkbox if MCQ_MULTI_ANSWER
                  <SingleAnswer key={option}>
                    <SingleAnswerInput
                      type="checkbox"
                      name={question?.id}
                      value={option}
                      checked={selectedOptions[question.id]?.includes(option)}
                      onChange={() => handleChange(option)}
                    />
                    {option}
                  </SingleAnswer>
                ) : (
                  <CustomRadioButton
                    key={option}
                    id={option}
                    label={option}
                    checked={selectedOptions[question.id]?.[0] === option}
                    onChange={() => handleChange(option)}
                  />
                )}
              </motion.div>
            ))}
          </Answers>

          </div>
        <SkipButton onClick={() => { setShowQuestions(false); }}>Skip</SkipButton>
        </motion.div>
      </QuestionNairBody>
      <QuestionNairFooter>
        <ButtonGroup>
          <Button
            type="button"
            variant="grey"
            onClick={prevStep}
            style={{ flex: 1 }}
            disabled={currentStep > 0 ? false : true}
          >
            Back
          </Button>

          <Button
            type="button"
            variant="primary"
            onClick={currentStep + 1 < totalQuestions ? nextStep : handleSubmit}
            disabled={!selectedOptions[question?.id] || selectedOptions[question?.id]?.length < 1}
            style={{ flex: 1 }}
          >
            {currentStep + 1 < totalQuestions ? 'Next' : 'Apply'}
          </Button>
        </ButtonGroup>
      </QuestionNairFooter>
    </QuestionNairWrapper>
  );
};

export default Questions;
