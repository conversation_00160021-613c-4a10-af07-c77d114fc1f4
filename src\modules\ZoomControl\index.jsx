import { ZoomControl } from '../MapController/index.styled';
import { useMap } from '../../context/MapContext';
import Icon from '@/components/shared/Icon';
import React, { useEffect, useRef, useState } from 'react';
import { debounce } from '../../shared/utils';

const ZoomController = () => {
  const {
    mapViewOptions: { zoom },
    zoomIn,
    zoomOut,
    updateZoom,
  } = useMap();
  const zoomValueRef = useRef(0);
  // const [currentZoom, setCurrentZoom] = useState(zoom || 0);
  // useEffect(() => {
  //   const handler = setTimeout(() => {
  //     if (zoom) setCurrentZoom(zoom);
  //   }, 10); // Delay in milliseconds
  //   // Cleanup the timeout if bearing/pitch changes before timeout completes
  //   return () => {
  //     clearTimeout(handler);
  //   };
  // }, []);
  // Synchronize initial zoom level
  useEffect(() => {
    if (zoom !== undefined) {
      // setCurrentZoom(zoom);
      zoomValueRef.current = zoom;
    }
  }, [zoom]);
  // useEffect(() => {
  //   const handler = setTimeout(() => {
  //     console.log({ currentZoom });
  //     updateZoom(currentZoom);
  //     // todo : currently this has issue on calling both function to sdk at the same time - this function needs to be merged
  //   }, 500); // Delay in milliseconds

  //   // Cleanup the timeout if bearing/pitch changes before timeout completes
  //   return () => {
  //     clearTimeout(handler);
  //   };
  // }, [currentZoom,updateZoom]);
  // Debounced update for zoom changes
  const updateZoomValue = debounce(() => {
    updateZoom(zoomValueRef.current);
  }, 20);
  // useEffect(() => {
  //   const handler = setTimeout(() => {
  //     updateZoom(currentZoom);
  //   }, 500);

  //   return () => clearTimeout(handler);
  // }, [currentZoom, updateZoom]);
  return (
    <ZoomControl>
      <button
        title="Zoom In"
        aria-label="Zoom In"
        className="zoom-in"
        onClick={() => {
          // zoomIn(zoom + 1);
          // setCurrentZoom((prev) => prev + 0.5);
          zoomValueRef.current = zoomValueRef.current + 0.5;
          updateZoomValue();
        }}
      >
        <Icon icon={'plus'} size={'18'} />
      </button>
      <button
        title="Zoom Out"
        aria-label="Zoom Out"
        className="zoom-out"
        onClick={() => {
          // zoomOut(zoom - 1);
          // setCurrentZoom((prev) => prev - 0.5);
          zoomValueRef.current = zoomValueRef.current - 0.5;
          updateZoomValue();
        }}
      >
        <Icon icon={'minus'} size={'18'} />
      </button>
    </ZoomControl>
  );
};

export default React.memo(ZoomController);
