import { useNavigate } from 'react-router-dom';
import RouterComponent from './router/router';
import 'bootstrap/dist/css/bootstrap.min.css';
import { MapProvider } from './context/MapContext';
import { useCallback } from 'react';

function App() {
  const navigate = useNavigate();

  const navigateTo = useCallback((path, data) => {
    navigate(path, { state: data });
  }, [navigate]);
  
  return (
    <MapProvider navigateTo={navigateTo}>
      <RouterComponent />
    </MapProvider>
  );
}

export default App;