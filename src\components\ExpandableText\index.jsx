import { useState } from 'react';

const ExpandableText = ({ previewLimit, children }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  const displayText =
    isExpanded || children.length <= previewLimit
      ? children
      : `${children.slice(0, previewLimit)}`;

  return (
    <>
      {displayText}
      {children.length > previewLimit && (
        <button onClick={toggleExpand}>
          {isExpanded ? ' Less' : '...More'}
        </button>
      )}
    </>
  );
};

export default ExpandableText;