pipeline {
    agent any
     tools {
        nodejs 'nodejs' 
    }
    triggers {
        githubPush()
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: "3"))
	    disableConcurrentBuilds()
    }
    environment {
        AWS_REGION = "ap-southeast-1"        
    }
    stages {


        stage('Set Environment') {
            steps {
                script {
                    // Set the SERVER environment variable based on the branch
                    if (env.BRANCH_NAME == 'develop') {
                        env.APP_NAME = "BECO_CAREROUTES_V2"
                        env.AWS_S3_BUCKET = "${BECO_CAREROUTE_V2_S3}"
                        env.AWS_CLOUDFRONT_DISTRIBUTION_ID = "${BECO_CAREROUTE_V2_CLOUDFRONT_DISTRIBUTION_ID}"
                        env.AWS_SECRET_MANAGER = "${BECO_CAREROUTE_V2_SECRET_MANAGER}"
                    } 
                    else if (env.BRANCH_NAME == 'meitra/main') {
                        env.APP_NAME = "MEITRA_CAREROUTES"
                        env.AWS_S3_BUCKET = "${MEITRA_CAREROUTES_V2_S3}"
                        env.AWS_CLOUDFRONT_DISTRIBUTION_ID = "${MEITRA_CAREROUTES_V2_CLOUDFRONT_DISTRIBUTION_ID}"
                        env.AWS_SECRET_MANAGER = "${MEITRA_CAREROUTES_V2_SECRET_MANAGER}"
                    } 
                    else if (env.BRANCH_NAME == 'hp/main') {
                        env.APP_NAME = "HP_CAREROUTES"
                        env.AWS_S3_BUCKET = "${HP_CAREROUTES_V2_S3}"
                        env.AWS_CLOUDFRONT_DISTRIBUTION_ID = "${HP_CAREROUTES_V2_CLOUDFRONT_DISTRIBUTION_ID}"
                        env.AWS_SECRET_MANAGER = "${HP_CAREROUTES_V2_SECRET_MANAGER}"
                    }
                    else if (env.BRANCH_NAME == 'gmr-hyd/main') {
                        env.APP_NAME = "GMRHYD_CAREROUTES"
                        env.AWS_S3_BUCKET = "${GMRHYD_CAREROUTES_V2_S3}"
                        env.AWS_CLOUDFRONT_DISTRIBUTION_ID = "${GMRHYD_CAREROUTES_V2_CLOUDFRONT_DISTRIBUTION_ID}"
                        env.AWS_SECRET_MANAGER = "${GMRHYD_CAREROUTES_V2_SECRET_MANAGER}"
                    }                    
                                        
                    else {
                        error "No config defined for branch: ${env.BRANCH_NAME}"
                    }
                }
            }
        }

        stage('SonarQube analysis') {
            steps {
                script {

                    slackSend (channel: 'sonarqube', color: '#0099ff', message: "SONAR ANALYSIS STARTED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]'")
                    def scannerHome = tool 'sonar-scanner';
                    withSonarQubeEnv('sonarqube') {
                    sh "${scannerHome}/bin/sonar-scanner -X"
                    }
                    
                    qualitygate = waitForQualityGate()
                    if (qualitygate.status == "OK") {

                        def msg = "${qualitygate.status}: ${env.JOB_NAME} #${env.BUILD_NUMBER}:\n${env.BUILD_URL}"
                        slackSend (channel: 'sonarqube', color: '#00FF00', message: "SONAR ANALYSIS SUCCESSFUL: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]'")
                    } else {
                        def color
                        def getURL = readProperties file: './.scannerwork/report-task.txt'
                        sonarqubeURL = "${getURL['dashboardUrl']}"
                        def msg = "${qualitygate.status}: ${env.JOB_NAME} #${env.BUILD_NUMBER}:\n${env.BUILD_URL}"
                        slackSend (channel: 'sonarqube', color: '#FF0000', message: "SONAR ANALYSIS FAILED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]':\n${sonarqubeURL}")

                    }
                }
            }
        }          

        stage('Installation, Build and Deployment') {
            agent {
                docker {
                    image 'node:22.12.0-slim'
                    args '-u root'
                }
            }
            steps {
                script {
                    slackSend(color: '#FFFF00', message: "${APP_NAME} BUILD STARTED: Job (<${env.BUILD_URL}|Open>)")
                }
                sh '''
                    npm install -g npm@10.9.0
                    apt-get update && apt-get install -y curl unzip jq
                    curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
                    unzip -o awscliv2.zip
                    ./aws/install -i /usr/local/aws-cli -b /usr/local/bin
                '''
                sh 'export PATH=$PATH:/usr/local/aws-cli/bin'
                withCredentials([[
                    $class: 'AmazonWebServicesCredentialsBinding',
                    accessKeyVariable: 'AWS_ACCESS_KEY_ID',
                    secretKeyVariable: 'AWS_SECRET_ACCESS_KEY',
                    credentialsId: 'BECO_API_CREDS'
                ]]) {
                    sh """aws secretsmanager get-secret-value --secret-id ${AWS_SECRET_MANAGER} --query SecretString --output text --region ${AWS_REGION} | jq -r 'to_entries[] | \"\\(.key)=\\(.value|tostring)\"' > .env"""                

                    sh '''
                        npm ci --legacy-peer-deps
                        npm run build        
                        aws s3 sync dist s3://${AWS_S3_BUCKET}
                        aws cloudfront create-invalidation --distribution-id ${AWS_CLOUDFRONT_DISTRIBUTION_ID} --paths "/*"
                    '''
                }
            }
        }
    }
    post {
        success {
            script {
                slackSend(color: '#00FF00', message: "${APP_NAME} BUILD SUCCESSFUL: Job (<${env.BUILD_URL}|Open>)")
            }
        }
        failure {
            script {
                slackSend(color: '#FF0000', message: "${APP_NAME} BUILD FAILED: Job (<${env.BUILD_URL}|Open>)")
            }
        }
    }
}
