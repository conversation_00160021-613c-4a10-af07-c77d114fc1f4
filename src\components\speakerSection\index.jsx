import { useMemo, useState } from "react";
import {
    CollapseArrow,
    LocationCollapseSection,
    LocationCollapseSectionHead,
    LocationCollapseSectionIns,
    LocationDetailSection,
    LocationFocusBody,
    LocationFocusWrapper,
    LocationHours,
    Flex
  } from '../../modules/MapView/index.styled';
  import { Icon as Iconify } from "@iconify/react";
  import Icon from '@/components/shared/Icon';
import theme from "../../config/theme";

const SpeakersSection = ({ data }) => {
  const [cardExpanded, setCardExpanded] = useState(false);

  const geTPersons = useMemo(() => {
    return data ? JSON.parse(data) : [];
  },[data]);
  if(geTPersons?.length===0){
    return <></>
  }
  return (
    <LocationDetailSection
      onClick={() => setCardExpanded(!cardExpanded)}
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.06)',
        marginBottom: 0,
      }}
    >
      <LocationFocusWrapper>
        <LocationCollapseSection>
          <LocationCollapseSectionIns>
            <LocationCollapseSectionHead>Speakers</LocationCollapseSectionHead>
          </LocationCollapseSectionIns>
        </LocationCollapseSection>
        <CollapseArrow
          expand={cardExpanded}
          style={{
            top: 14,
          }}
        >
          <Icon icon={'chevron-right'} size={16} />
        </CollapseArrow>
      </LocationFocusWrapper>
      <LocationFocusBody expand={cardExpanded}>
        <LocationHours>
          <div style={{ marginTop: '0' }}>
            {geTPersons.map((person, index) => {
              return (
                <Flex
                  key={index}
                  align="center"
                  style={{
                    marginBottom: '8px',
                    border: '1px solid rgb(230, 230, 230)',
                    padding: '8px 7px',
                    borderRadius: '16px',
                    backgroundColor: '#fff',
                  }}
                >
                  <div
                    style={{
                      marginRight: '4px',
                      padding: '0',
                      flex: '0 0 auto',
                      width: 'auto',
                      color: theme.colors.primary,
                      marginTop: '-2px',
                    }}
                  >
                    <Iconify icon="solar:microphone-3-bold-duotone" width="23" height="23" />
                  </div>
                  <div
                    style={{
                      fontSize: '13px',
                      textAlign: 'left',
                      flex: '1 0 0%',
                    
                    }}
                  >
                    <Flex dir="column" gap="0px">
                      <p
                        style={{
                          padding: '0',
                          fontWeight: '600',
                          width: 'auto',
                          margin: '0',
                          fontSize: '14px',
                          lineHeight: '1.2',
                          color: 'rgba(0,0,0,1)',
                        }}
                      >
                        {person?.name}
                      </p>
                      <span
                        style={{
                          marginRight: '4px',
                          padding: '0',
                          flex: '0 0 auto',
                          width: 'auto',
                          color: 'rgba(0,0,0,.5)',
                            lineHeight: '14px',
    marginTop: '3px'
                        }}
                      >
                        {person?.role}
                      </span>
                    </Flex>
                  </div>
                </Flex>
              );
            })}
          </div>
        </LocationHours>
      </LocationFocusBody>
    </LocationDetailSection>
  );
};

export default  SpeakersSection
