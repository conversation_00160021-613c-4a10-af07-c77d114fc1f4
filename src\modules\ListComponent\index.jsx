import { useEffect, useMemo, useRef, useState } from 'react';
import { VariableSizeList } from 'react-window';
import { List, ListIns, ListItem, ListItemImage, ListItemSegment, ListItemTitleUnit } from '../MapView/index.styled';
import Icon from '@/components/shared/Icon';
import useFormattedHours from '../../hooks/UseFormattedHours';
import { processOperationHours } from '../../shared/utils';

export const ListComponent = ({ listData = [], onSelect, emptyMessage = 'No Data Found' }) => {
  const [data, setData] = useState([]);
  const [sizeMap, setSizeMap] = useState({});
  const listRef = useRef();
  const iconBaseUrl = import.meta.env.VITE_APP_BASE_URL_ICON;

  useEffect(() => {
    const sortableData = (listData||[])
      .filter((it) => it.sortOrder)
      .sort((a, b) => {
        if (a.sortOrder === b.sortOrder) {
          return a.name.localeCompare(b.name); // Sort by name if sortOrder is the same
        }
        return a.sortOrder - b.sortOrder; // Sort by sortOrder first
      });
    const nonSortableData = (listData||[]).filter((it) => it.sortOrder === 0).sort((a, b) => a.name.localeCompare(b.name));
    setData([...sortableData, ...nonSortableData]);
    // Reset size cache when data changes
    listRef.current?.resetAfterIndex(0);
    setSizeMap({}); // Clear previous size measurements
  }, [listData]);

  const Row = ({ index, style, data }) => {
    // console.log({index,loc:data.items[index] })
    const rowRef = useRef();
    const location = data.items[index];
    const category = location?.categories?.[0]?.name;
    const { todaysData } = processOperationHours(location);
    const isOpen = todaysData?.opens && todaysData?.closes;
    const isLocation = location.type !== 'AMENITIES';
    const usingCategoryIcon = useMemo(
      () => !location?.logo && !!location?.categories?.[0]?.icon,
      [location]
    );
    const bgColor = useMemo(() => {
      return location?.categories?.[0]?.color?.hex || '#41648B';
    }, [location]);
    const imageUrl = useMemo(() => {
      if (isLocation) {
        return location?.logo || location?.categories?.[0]?.icon;
      }
      return (
        location?.categories?.[0]?.icon ||
        `${iconBaseUrl}${location?.amenity?.toLowerCase().replace('_', '-')}.svg`
      );
    }, [isLocation, location]);

    // Measure row height after render
    useEffect(() => {
      if (rowRef.current) {
        const height = rowRef.current.getBoundingClientRect().height+8;
        if (sizeMap[index] !== height) {
          setSizeMap(prev => ({ ...prev, [index]: height }));
          listRef.current?.resetAfterIndex(index);
        }
      }
    }, [index, location]); // Re-measure when location data changes

    return (
      <div style={{ ...style, padding: '0 5px' }}>
        <div ref={rowRef}>
          <List className={usingCategoryIcon ? 'category-icon' : ''} onMouseDown={() => data.onSelect && data.onSelect(location)} key={`${location.name}-${index}`}>
            <ListIns>
              <ListItemImage style={bgColor ? { backgroundColor: bgColor } : undefined} isCategoryIcon={true}>
                {imageUrl ? (
                  <img src={imageUrl} alt={`${location.name}`} width={50} />
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M19.717 20.362C21.143 19.585 22 18.587 22 17.5c0-1.152-.963-2.204-2.546-3C17.623 13.58 14.962 13 12 13s-5.623.58-7.454 1.5C2.963 15.296 2 16.348 2 17.5s.963 2.204 2.546 3C6.377 21.42 9.038 22 12 22c3.107 0 5.882-.637 7.717-1.638"
                      opacity="0.5"
                    />
                    <path
                      fill="currentColor"
                      fillRule="evenodd"
                      d="M5 8.515C5 4.917 8.134 2 12 2s7 2.917 7 6.515c0 3.57-2.234 7.735-5.72 9.225a3.28 3.28 0 0 1-2.56 0C7.234 16.25 5 12.084 5 8.515M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </ListItemImage>
              <ListItem style={{ gap: '1px'}}>
                <ListItemSegment>
                  <ListItemTitleUnit>
                    <p className="list-title">{location.name}</p>
                  </ListItemTitleUnit>
                </ListItemSegment>
                {/* {isLocation && ( */}
                  <ListItemSegment>
                    <ListItemTitleUnit>
                      <p>{location?.floor?.shortName}</p>
                    </ListItemTitleUnit>
                    {category && <ListItemTitleUnit>
                      <p>{category || ''}</p>
                    </ListItemTitleUnit>}
                  </ListItemSegment>
                {/* )} */}
              </ListItem>
            </ListIns>
          </List>
        </div>
      </div>
    );
  };

  const getItemSize = index => sizeMap[index] || 84; // Fallback to default height

  if (data.length === 0) {
    return (
      <List style={{ padding: '20px', textAlign: 'center' }}>
        <ListIns>
          <ListItem>
            <ListItemSegment style={{ justifyContent: 'center' }}>
              <ListItemTitleUnit style={{ justifyContent: 'center' }}>
                <p>{emptyMessage}</p>
              </ListItemTitleUnit>
            </ListItemSegment>
          </ListItem>
        </ListIns>
      </List>
    );
  }

  return (
    <VariableSizeList
      ref={listRef}
      height={700}
      width="100%"
      itemSize={getItemSize}
      estimatedItemSize={84} // Helps with scrollbar accuracy
      itemCount={data.length}
      itemData={{
        items: data,
        onSelect,
        iconBaseUrl
      }}
    >
      {Row}
    </VariableSizeList>
  );
};

export default ListComponent;