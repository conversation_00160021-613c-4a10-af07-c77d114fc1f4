import {
  ButtonGroup,
  DepartmentDetails,
  Flex,
  ItemCard,
  ItemCardIns,
  ListItem,
  ListItemImage,
  ListItemSegment,
  ListItemTitleUnit,
  Panel,
  PanelBody,
  PanelHeader,
  PanelInner,
  SearchArea,
  TabArea,
} from '../MapView/index.styled';
import Icon from '@/components/shared/Icon';
import Button from '@/components/shared/button/button';
import { Tab, Tabs } from 'react-bootstrap';
import { useMap } from '../../context/MapContext';
import { convertTime, processOperationHours } from '../../shared/utils';
import { useState } from 'react';
import ExpandableText from '../../components/ExpandableText';
import EmptyCard from '../../components/shared/EmptyCard';

const DepartmentPanel = ({ open = false, onNavigate }) => {
  const { departments } = useMap();
  const [search, setSearch] = useState('');
  const clickDirection = (data) => {
    onNavigate?.(data);
  };
  return (
    <Panel open={open}>
      <PanelInner>
        {(departments || []).length > 0 ? (
          <>
            <PanelHeader>
              <SearchArea>
                <span>
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect
                      opacity="0.5"
                      x="17.0365"
                      y="15.1223"
                      width="8.15546"
                      height="2"
                      rx="1"
                      transform="rotate(45 17.0365 15.1223)"
                      fill="currentColor"
                    ></rect>
                    <path
                      d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                      fill="currentColor"
                    ></path>
                  </svg>
                </span>
                <input
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  type="text"
                  name="search"
                  placeholder="Search..."
                />
              </SearchArea>
            </PanelHeader>
            <PanelBody>
              {(departments || [])
                .filter((item) => {
                  if (search) {
                    const regex = new RegExp(search, 'i');
                    // Filter the array based on the `name` property
                    return regex.test(item.name);
                  } else {
                    return true;
                  }
                })
                .map((it, index) => {
                  const { externalData, name, floor, phone, nodeId, description } = it;
                  const { todaysData } = processOperationHours(nodeId);
                  const tele = phone?.phone;
                  return (
                    <ItemCard key={index}>
                      <ItemCardIns>
                        <Flex
                          align="center"
                          gap={'12px'}
                          style={{ borderBlockEnd: '1px solid #e6e6e6', padding: '10px' }}
                        >
                          <ListItemImage>
                            <img
                              aria-hidden="true"
                              src={it?.logo || 'https://cdn.becomap.com/media/store_images/unnamed-2.jpg'}
                              alt={name}
                            />
                          </ListItemImage>
                          <ListItem>
                            <ListItemSegment>
                              <ListItemTitleUnit>
                                <p className="list-title">{name}</p>
                              </ListItemTitleUnit>
                            </ListItemSegment>
                            <ListItemSegment>
                              <ListItemTitleUnit>
                                <p>{floor?.shortName}</p>
                              </ListItemTitleUnit>
                              <ListItemTitleUnit>
                                <p className={todaysData?.opens && todaysData?.closes ? 'opened' : 'closed'}>
                                  {todaysData?.opens && todaysData?.closes ? 'Open' : 'Closed'}
                                </p>
                              </ListItemTitleUnit>
                            </ListItemSegment>
                          </ListItem>
                        </Flex>
                        <Flex dir="column">
                          {externalData?.doctors?.length ? (
                            <TabArea>
                              <Tabs defaultActiveKey="info">
                                <Tab eventKey="info" title={'Info'}>
                                  <DepartmentDetails>
                                    <ExpandableText previewLimit={70}>{description || ''}</ExpandableText>
                                  </DepartmentDetails>
                                </Tab>

                                <Tab eventKey="doctors" title={'Doctors'}>
                                  <div className="docs">
                                    {externalData?.doctors?.map((it, index) => {
                                      return (
                                        <span className="docs-item" key={index}>
                                          {it}
                                        </span>
                                      );
                                    })}
                                  </div>
                                </Tab>
                              </Tabs>
                            </TabArea>
                          ) : (
                            <DepartmentDetails style={{ padding: '10px' }}>
                              <ExpandableText previewLimit={70}>{description || ''}</ExpandableText>
                            </DepartmentDetails>
                          )}
                          <ButtonGroup
                            style={{
                              padding: '10px',
                              backgroundColor: 'rgb(0 0 0 / 7%)',
                            }}
                          >
                            {tele && (
                              <a href={`tel:${tele}`}><Button
                                variant="grey"
                                icon={<Icon icon="phone-call-01" />}
                              /></a>
                            )}
                            <Button
                              onClick={() => clickDirection(it)}
                              type="button"
                              variant="primary"
                              style={{ flex: 1 }}
                            >
                              Navigate
                            </Button>
                          </ButtonGroup>
                        </Flex>
                      </ItemCardIns>
                    </ItemCard>
                  );
                })}
            </PanelBody>
          </>
        ) : (
          <EmptyCard />
        )}
      </PanelInner>
    </Panel>
  );
};

export default DepartmentPanel;
