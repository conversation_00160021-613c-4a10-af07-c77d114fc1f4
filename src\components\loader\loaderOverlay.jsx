import React from 'react';
import styled, { keyframes } from 'styled-components';
const Container = styled.div`
  position: relative;
`;

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const OverlayWrapper = styled.div`
  position: fixed;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
`;

const Spinner = styled.div`
  width: 48px;
  height: 48px;
  border: 4px solid rgba(37, 99, 235, 0.1);
  border-left-color: #2563eb;
  border-radius: 50%;
  animation: ${spin} 1s linear infinite;
`;

export function LoaderOverlay({ loading, children }) {
  return (
    <Container>
      {children}
      {loading && (
        <OverlayWrapper>
          <Spinner />
        </OverlayWrapper>
      )}
    </Container>
  );
}
