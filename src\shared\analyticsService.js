import Analytics from 'analytics';
import googleAnalytics from '@analytics/google-analytics';

const MEASUREMENT_ID = import.meta.env.VITE_APP_GOOGLE_ANALYTICS_ID;

// Initialize analytics only if the Measurement ID exists
const analytics = MEASUREMENT_ID
  ? Analytics({
      app: "hp-events", // Change this to your app name
      plugins: [
        googleAnalytics({
          measurementIds: [MEASUREMENT_ID],
        }),
      ],
    })
  : null;

// Function to track page views
export const trackPageView = (pageName) => {
  if (analytics) {
    analytics.page({ title: pageName });
  }
};

// Function to track custom events
export const trackEvent = (eventName, properties = {}) => {
  if (analytics) {
    analytics.track(eventName, properties);
  }
};


// Function to check if analytics is initialized
export const isAnalyticsEnabled = () => !!analytics;