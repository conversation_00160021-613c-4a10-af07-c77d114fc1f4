import { motion } from 'framer-motion';
import { style } from 'framer-motion/client';
import styled, { css } from 'styled-components';

export const BottomBar = styled.div`
  background-color: #fff;
  border-color: #e5e7eb;
  border-top-width: 1px;
  width: 100%;
  height: 54px;
  z-index: 99;
  left: 0;
  bottom: 0;
  -webkit-box-shadow:
    0 0.156rem 0.156rem rgba(0, 25, 75, 0.0509804),
    0 0 0.5rem rgba(0, 25, 75, 0.0509804),
    0 0.313rem 0.313rem rgba(0, 25, 75, 0.0509804);
  box-shadow:
    0 0.156rem 0.156rem rgba(0, 25, 75, 0.0509804),
    0 0 0.5rem rgba(0, 25, 75, 0.0509804),
    0 0.313rem 0.313rem rgba(0, 25, 75, 0.0509804);
  position: fixed;
  /* height: 4.125rem;
  width: 100%;
  transition: width 0.25s ease-in-out;
  position: absolute;
  z-index: 99;
  bottom: 0; */
`;

export const BottomBarInner = styled.div`
  font-weight: 500;
  grid-template-columns: repeat(${({ count }) => count}, minmax(0, 1fr));
  max-width: 32rem;
  height: 100%;
  display: grid;
  margin-left: auto;
  margin-right: auto;
`;

export const NavBar = styled.div`
  /* font-weight: 500;
  grid-template-columns: 1fr; 
  grid-auto-rows: minmax(70px, auto); */
  /* grid-template-columns: repeat(5, minmax(0, 1fr)); */
  /* max-width: 32rem; */
  /* height: 100%;
  display: grid;
  margin-left: auto;
  margin-right: auto; */
  font-weight: 500;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  max-width: 100%;
  height: 57px;
  display: grid;
  margin-left: auto;
  margin-right: auto;

  display: inline-block;
  height: 64px;
  position: relative;
  width: 64px;
  flex: 1;
`;
export const NavItem = styled.div`
  padding-left: 12px;
  padding-right: 12px;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  display: inline-flex;
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
  cursor: pointer;
  text-transform: none;
  /* min-width: 50px;
  transition: background-color 0.2s ease-out;
  border: none;
  background-color: transparent;
  color: rgb(60 60 67 / 60%);
  text-align: left;
  font-size: 11px;
  padding: 8px 5px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  justify-content: center; */

  /* order: ${({ order }) => order}; */
  span {
    /* width: 40px;
    height: 40px;
    background-color: ${({ active, theme }) => (active ? theme.colors.primary : 'rgb(0 0 0 / 4%)')};
    border-radius: 14px;
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    transition: background-color 0.1s ease-out; */
    /* background-color: ${({ active, theme }) =>
      active ? `rgba(${theme.colors.primaryRgb}, 0.2)` : 'transparent'}; */
    color: ${({ active, theme }) => (active ? theme.colors.primary : 'rgb(60 60 67 / 60%)')};

    transition:
      background-color 0.2s ease-out,
      color 0.2s ease-out;
    border-radius: 8px;
    box-sizing: border-box;
    min-height: 26px;
    /* padding: 4px; */
    min-width: 30px;
    align-items: center;
    justify-content: center;
    display: inline-flex;

    svg {
      display: block;
      height: 100%;
      width: 100%;
      height: 30px;
      width: 30px;
    }
  }
  p {
    /* max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-wrap: break-word;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-smooth: always;
    -webkit-text-size-adjust: none;
    -moz-text-size-adjust: none;
    text-size-adjust: none;
    margin: 0;
    overflow-wrap: break-word; */
    color: ${({ active, theme }) => (active ? theme.colors.primary : 'rgb(60 60 67 / 70%)')};
    font-weight: ${({ active }) => (active ? 500 : 400)};
    /* 
    line-height: 18px;
    text-align: start;
    font-size: 11px;
    letter-spacing: 0.2px; */
    /* letter-spacing: calc(var(--gFX-3g, .8rem) / 8* .1); */
    /* color: #70757a;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: none; */
    /* color: #2e2e2e; */
    font-size: 11px;
    line-height: 15px;
    margin: 0;
  }
`;

export const MapContainer = styled.div`
  position: absolute;
  top: 0px;
  z-index: 0;
  width: 100%;
  height: 100%;
  touch-action: auto;
  overflow: hidden;
  background-color: #f1efec;
  overscroll-behavior: contain;
`;

export const Toolbars = styled.div`
  display: flex;
  width: 100%;
  height: 100%;
  align-items: flex-start;
  max-width: 400px;
  position: relative;
  pointer-events: none;
  z-index: 52;
  @media (max-width: 734px) {
    max-width: unset;
    align-items: unset;
  }
`;

export const ToolbarsInner = styled.div`
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: auto;
  z-index: 1;
  margin: 20px 20px 20px;
  max-height: min(100% - 20px, 844px);
  pointer-events: none;
  gap: 12px;
  z-index: 2;
  padding-top: ${({ startNavigation }) => startNavigation && '0'};

  @media (max-width: 734px) {
    margin: 0px;
    justify-content: ${({ isDirection, startNavigation }) =>
      startNavigation ? 'space-between' : isDirection ? 'flex-end' : 'flex-start'};
    max-height: 100%;
    padding-top: 0;
  }
`;

export const StateCard = styled.div`
  touch-action: none;
  color: rgb(0, 0, 0);
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  z-index: 3;
  border-radius: ${({ fullRadius }) => (fullRadius ? '18px' : '20px 20px')};
  background-color: #fff9;
  /* border: 1px solid ${({ dark }) => (dark ? 'transparent' : '#ededed')}; */
  border-top: none;

  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.05);
  filter: drop-shadow(1px 1px 5px rgba(0, 0, 0, 0.05));
  -webkit-backdrop-filter: blur(50px);
  backdrop-filter: blur(50px);
  height: 100%;
  width: 360px;
  max-height: calc(100dvh - 20px);
  pointer-events: all;
  position: relative;
  transform: translateZ(0px);
  padding-top: 25px;

  @media (max-width: 734px) {
    width: 100%;
    flex: unset;
    border-radius: ${({ fullRadius }) => (fullRadius ? '18px' : '20px 20px 0 0 ')};
    padding-top: 12px;

    ${({ direction }) =>
      direction &&
      css`
        border-radius: ${({ fullRadius }) => (fullRadius ? '18px' : '0 0 20px 20px')};
      `}

    ${({ startNavigation }) =>
      startNavigation &&
      css`
        height: auto;
      `}
  }
`;

export const StateCardHeader = styled.div`
  width: 100%;
  /* max-height: 44px; */
  padding: 0 15px 15px;
`;

export const StateCardBody = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1 1 auto;
  justify-content: flex-start;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0 15px 15px;
  .bottom-sheet-header {
    display: none;
  }
  ${({ card }) =>
    card === 'location' &&
    css`
      @media (max-width: 734px) {
        .bottom-sheet-header {
          border-radius: 16px 16px 0 0;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 8px;
          .handle {
            width: 36px;
            height: 5px;
            background-color: rgba(0, 0, 0, 0.14);
            border-radius: 5px;
          }
        }
        ${LocationDetailsCardHeader} {
          left: auto;
          right: 0;
          gap: 10px;
          flex-direction: row-reverse;
        }

        ${ListItemImage} {
          border-radius: 12px;
        }
        ${LocationDetailsTitle} {
          margin: 0;
          padding-right: 75px;
        }
      }
    `};
`;

export const StateCardHead = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  h5 {
    font-size: 14px;
    font-weight: 600;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 1.235rem;
    margin: 0 0 14px 0;
  }
`;

export const ExpandArrow = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  & > div {
    pointer-events: none;
    width: 22px;
    height: 22px;
    display: inline-block;
    position: relative;
    margin: 0 16px;
    background-color: #00378f0d;
    padding: 4px;
    border-radius: 50px;
    span {
      top: 10px;
      position: absolute;
      width: 8px;
      height: 2px;
      background-color: #101723f2;
      display: inline-block;
      transition: all 0.2s ease;
      &:first-of-type {
        left: 3.8px;
        transform: ${({ open }) => (open ? 'rotate(-45deg)' : 'rotate(45deg)')};
      }
      &:last-of-type {
        right: 3.8px;
        transform: ${({ open }) => (open ? 'rotate(45deg)' : 'rotate(-45deg)')};
      }
    }
  }
`;

export const CategoriesListWrapper = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
`;

export const CategoriesListItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 12px;
  gap: 10px;
  flex: 0 0 auto;
  width: 31.33333333%;
  min-height: 110px;
  cursor: pointer;
  position: relative;
  background-color: #fff;
  text-decoration: none;
  border-radius: 25px;
  align-items: center;
  color: #000;

  ${({ isList }) =>
    isList &&
    css`
      min-height: auto;
      width: 100%;
      padding: 10px 16px;
      flex-direction: row;
      text-align: left;
      align-items: center;
      justify-content: space-between;
      border-radius: 12px;
    `}
  @media (max-width: 431px) {
    width: 30.33333333%;
    min-height: 100px;

    ${({ isList }) =>
      isList &&
      css`
        min-height: auto;
        width: 100%;
      `}
  }
  img {
    /* width: 40px;
    height: 40px;
    background-color: rgb(60 60 67 / 30%);
    border-radius: 50px;
    padding: 5px;

    @media (max-width: 431px) {
      width: 35px;
      height: 35px;
    } */
    filter: invert(0.6);
  }
  h5 {
    margin: 0px;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 13px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
  }
  p {
    color: rgb(60 60 67 / 60%);
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
  }
  &:hover {
    box-shadow: inset 3px 3px 3px 0px rgb(0 0 0 / 10%);
  }
`;
export const NavigationTitleHead = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 0px;
  gap: 8px;
  /* color: #708199; */
  line-height: 30px;
  font-size: 1.294em;
  font-weight: 600;
  margin: 0 0 10px;
  @media (max-width: 763px) {
    margin: 0 0 8px;
  }
  h2 {
    font-size: inherit;
    line-height: inherit;
    font-family: inherit;
    color: inherit;
    margin: 0;
    flex: 1;
  }

  button {
    appearance: none;
    border: none;
    background: none;
    border: none;
    cursor: pointer;
  }
`;

export const NavigationTitleHeadActions = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  gap: 12px;
`;
export const ShareButton = styled.button`
  appearance: none;
  border: none;
  /* background: none; */
  border: none;
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  border-radius: 4px;
  width: 30px;
  transform: translateY(-50%);
  font-size: 14px;
  opacity: 0.7;
  &:hover {
    background-color: #0000000f;
  }
`;

export const NavigationSearch = styled.div`
  box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary}; inset;
  display: flex;
  position: relative;
  z-index: 2;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: 44px;
  width: 100%;
  padding: 0px 1rem;
  background-color: #fff;
  border-radius: 15px;
  gap: 8px;
  color: #000;
  border: 2px solid
    hsla(
      var(--ue-base-form-elements-color_h, 0),
      var(--ue-base-form-elements-color_s, 0%),
      var(--ue-base-form-elements-color_l, 0%),
      var(--ue-base-form-elements-color_t, 0.07)
    );

  /*background-color: hsla(var(--ue-secondary-color_h, 0),var(--ue-secondary-color_s, 0%),var(--ue-secondary-color_l, 100%),var(--ue-secondary-color_t, 1));
  box-shadow: 0 1px 3px 0 var(--ue-base-shadow-color, rgba(0, 0, 0, .1)); */

  &:focus-within {
    border: 2px solid ${({ theme }) => theme.colors.primary};;

    svg {
      color: hsla(
        var(--ue-default-font-color_h, 0),
        var(--ue-default-font-color_s, 0%),
        var(--ue-default-font-color_l, 33.33%),
        var(--ue-default-font-color_t, 1)
      );
    }
  }
`;

export const SearchInput = styled.input`
  display: block;
  appearance: none;
  border: none;
  letter-spacing: 0px;
  font-size: 16px;
  transition-property: width, padding, margin;
  transition-duration: 0.5s;
  flex: 1 1 0%;
  width: 100%;
  background-color: transparent;
  font-size: 16px;
  height: 100%;
  color: inherit;

  &::placeholder {
    /* color: #708199; */
    font-style: normal;
    font-weight: 500;
  }
  &:hover,
  &:focus {
    outline: none;
  }
`;

export const CategoriesList = styled.div`
  width: 100%;
`;

export const CategoriesListInner = styled.div`
  /* display: grid;
  grid-template-columns: repeat(4, minmax(0px, 1fr));
  gap: 15px;
  justify-content: center;
  transition: height 0.5s;
  overflow: hidden; */
  display: flex;
  gap: 28px;
  justify-content: flex-start;
  transition: height 0.5s;
  overflow: hidden;
  flex-wrap: nowrap;
`;

export const CategoryButton = styled.button`
  /* text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 0;
  outline: 0;
  background: transparent; */

  background: none;
  border: none;
  font: inherit;
  outline: none;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 60px;
  flex: 0 0 60px;
  min-width: 60px;
  border-radius: 0.5rem;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
`;
export const CategoryImage = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.04);
  overflow: hidden;

  color: hsla(
    var(--ue-default-font-color_h, 0),
    var(--ue-default-font-color_s, 0%),
    var(--ue-default-font-color_l, 33.33%),
    calc(var(--ue-default-font-color_t, 1) - 0.6)
  );
  border-radius: 20px;
  border: 1px solid
    hsla(
      var(--ue-base-form-elements-color_h, 0),
      var(--ue-base-form-elements-color_s, 0%),
      var(--ue-base-form-elements-color_l, 0%),
      var(--ue-base-form-elements-color_t, 0.07)
    );

  /* box-shadow: 0 1px 3px 0 var(--ue-base-shadow-color, rgba(0, 0, 0, 0.1)); */
  /* background-color: hsla(
    var(--ue-base-form-elements-color_h, 0),
    var(--ue-base-form-elements-color_s, 0%),
    var(--ue-base-form-elements-color_l, 0%),
    var(--ue-base-form-elements-color_t, 0.07)
  ); */

  img {
    max-width: 58px;
    max-height: 58px;
    width: 100%;
    height: auto;
    color: ${({ theme }) => theme.colors.primary};
    object-fit: contain;
  }
`;
export const CategoryLabel = styled.p`
  color: #475569;
  letter-spacing: 0px;
  margin: 0px;
  font-size: 12px;
  line-height: 16px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-word;
  text-overflow: ellipsis;
  margin-top: 0.5rem;
`;

export const List = styled.div`
  width: 100%;
  border-radius: 20px;
  background: #fff;
  line-height: 1.324em;
  position: relative;
  display: flex;
  flex-direction: row;
  padding: 10px 0;
  background-color: #fff;
  text-decoration: none;
  cursor: pointer;
  padding-right: 0;
  transition: all 0.14s ease-out;
  &:not(:last-child) {
    margin-bottom: 10px;
  }
  * {
    transition: all 0.14s ease-out;
  }
  &:hover {
    box-shadow:
      0 5px 15px #0000000d,
      0 2px 6px #0000000d;
    transform: scale(1.01);
  }
`;

export const ListIns = styled.div`
  overflow: hidden;
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  gap: 12px;
  /* pointer-events: none; */
`;

export const ListItem = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
export const ListItemImage = styled.div`
  --size: 50px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  padding: 3px;
  width: var(--size);
  height: var(--size);
  flex: 0 0 var(--size);
  background-color: #fff;
  overflow: hidden;
  color: hsla(
    var(--ue-default-font-color_h, 0),
    var(--ue-default-font-color_s, 0%),
    var(--ue-default-font-color_l, 33.33%),
    1
  );
  border-radius: 16px;
  border: 2px solid
    hsla(
      var(--ue-base-form-elements-color_h, 0),
      var(--ue-base-form-elements-color_s, 0%),
      var(--ue-base-form-elements-color_l, 0%),
      var(--ue-base-form-elements-color_t, 0.07)
    );

  img {
    max-width: calc(var(--size) - 3px);
    height: auto;
  }

  ${({ isCategoryIcon }) =>
    isCategoryIcon &&
    css`
      --size: 40px;
      border-radius: 12px;
      border: none;
      img {
        max-width: 24px;
      }
    `}
`;
export const ListItemIns = styled.div`
  align-items: center;
  display: flex;
  gap: 1rem;
  height: inherit;
  min-width: 0;
  width: 100%;
`;

export const ListItemSegment = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
  gap: 8px;
  color: rgb(60 60 67 / 60%);
`;

export const ListItemTitleUnit = styled.div`
  display: flex;
  align-items: center;
  flex-direction: row;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  p {
    color: rgb(60 60 67 / 60%);
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    text-align: left;
    &.list-title {
      font-weight: 600;
      font-size: 15px;
      line-height: 1.2;
      white-space: normal;
      color: #000;
    }

    &.closed {
      color: #f24933;
    }

    &.opened {
      color: #65c366;
    }
  }

  &:not(:first-child) {
    &::before {
      content: '';
      width: 5px;
      height: 5px;
      background: rgb(60 60 67 / 60%);
      border-radius: 50px;
      margin-right: 8px;
      display: inline-block;
    }
  }
`;

export const ListImageWrapper = styled.div`
  /* 
  width: 35px;
  height: 35px;
  
  flex: 0 0 auto; */
  /* background-color: #f5f5f5; */
  /* border-radius: 4px; */
  flex: 0 0 30px;
  height: 30px;
  width: 30px;
  overflow: hidden;
  /* display: flex;
  align-items: center;
  justify-content: center; */

  img {
    width: 100%;
    height: 100%;
    display: block;
    margin: 0 auto;
    object-fit: contain;
  }
`;

export const ListItemContent = styled.div`
  /* font-size: 14px;
  letter-spacing: 0px;
  line-height: 1.267em;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  min-width: 0px;
  margin-left: 1rem; */
  overflow: hidden;
  & > p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #101723f2;
    line-height: 1.4;
    margin: 0px;
    font-style: normal;
    font-size: 16px;
    font-weight: 500;
  }
  & > span {
    align-items: center;
    display: flex;
    gap: 0.3125rem;
    color: #07173199;
    flex: 1 1;
    font-style: normal;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.2;
  }
`;

export const LocationDetailsCard = styled.article`
  display: flex;
  flex-direction: column;
  max-height: calc(100dvh - 2rem);
  position: relative;
  gap: 12px;
`;

export const LocationDetailsCardHeader = styled.div`
  position: absolute;
  top: 0;
  left: 15px;
  right: 15px;
  font-size: 15px;
  z-index: 10;
  outline: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const TopActionBtn = styled.div`
  width: 30px;
  height: 30px;
  background-color: #00000014;
  border-radius: 50px;
  -webkit-backdrop-filter: blur(50px);
  backdrop-filter: blur(50px);
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const LocationDetailsCardSegment = styled.div`
  display: flex;
  gap: 0.25rem;
  justify-content: space-between;
  /* padding: 1rem 1rem 0; */
  flex-direction: ${({ direction }) => (direction === 'column' ? 'column' : 'row')};

  * {
    cursor: pointer;
  }
`;

export const LocationCoverPhoto = styled.div`
  margin: 0;
  width: 100%;
  border-radius: 16px;
  background-color: #f2f2f6;
  position: relative;
  &::before {
    background-color: rgba(0, 0, 0, 0.41);
    border-radius: 12px;
    height: 100%;
    pointer-events: none;
    position: absolute;
    top: 0;
    pointer-events: none;
    width: 100%;
    content: '';
  }
`;
export const LocationCoverImage = styled.div`
  display: block;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 1;
  will-change: background-image;
  transition: opacity 0.3s linear;
  height: 180px;
  border-radius: 16px;
`;

export const LocationDetailsTitle = styled.div`
  margin: 4px 0 0;
  width: 100%;

  h1 {
    padding: 0;
    margin: 0 0 2px;
    font-size: 1.294rem;
    line-height: 1.529rem;
    font-weight: 700;
    overflow-wrap: break-word;
    width: 100%;
  }

  &.sm {
    h1 {
      font-size: 1.125rem;
      line-height: 1.25rem;
    }
  }
`;

export const LocationDetailsTitleUnit = styled.div`
  display: flex;
  align-items: center;
  flex-direction: row;
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  gap: 8px;
  p {
    color: rgb(60 60 67 / 60%);
    font-size: 12px;
    line-height: 1.4;
    margin: 0;
    &.list-title {
      font-weight: 600;
      font-size: 15px;
      line-height: 1.5;
      white-space: normal;
      color: #000;
    }

    &.closed {
      color: #f24933;
      font-weight: 500;
    }

    &.opened {
      font-weight: 500;
      color: #65c366;
    }

    &:not(:first-child) {
      &::before {
        content: '';
        width: 5px;
        height: 5px;
        background: rgb(60 60 67 / 60%);
        border-radius: 50px;
        margin-right: 8px;
        display: inline-block;
      }
    }
  }
`;

export const LocationDetailsCardHeaderImage = styled.div`
  align-self: start;
  display: flex;
  justify-content: center;
  align-items: center;

  /* border: 1px dashed #00378f14; */
  /* box-shadow:
    rgba(0, 0, 0, 0.08) 0px 2px 4px -2px,
    rgba(0, 0, 0, 0.04) 0px 4px 8px -2px; */

  /* box-shadow:
    12px 12px 32px rgba(13, 39, 80, 0.25),
    -10px -10px 15px white,
    inset -15px -15px 20px rgba(255, 255, 255, 0.7),
    inset 8px 8px 30px rgba(13, 39, 80, 0.2); */
  background-color: #fff;
  padding: 5px;
  min-width: 50px;
  width: 50px;
  height: 50px;
  /* margin-right: 12px; */
  border-radius: 8px;
  margin-bottom: 15px;
  border-radius: 8px;
  border: 1px dashed #cfcfce;
  background-color: #fff;
  img {
    width: 100%;
    height: auto;
    object-fit: contain;
  }
`;

export const LocationDetailsCardSubtitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
  line-height: 1rem;
  margin: 0.3rem 0;
  /* padding: 0 1rem; */

  & > span {
    color: #07173199;
    font-size: 0.875rem;
    font-weight: 500;
    font-style: normal;
  }
`;

export const Badge = styled.span`
  border-width: 1px;
  border-style: dashed;
  border-color: ${({ color }) => (color ? color : '#0d0d0d')};
  color: ${({ color }) => (color ? color : '#0d0d0d')}!important;
  display: block !important;
  padding: 1px 6px !important;
  border-radius: 5px !important;
  font-size: 12px !important;
  text-transform: capitalize !important;
  font-weight: 500 !important;
  line-height: normal !important;
  background-color: #fff;
`;

export const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  margin: 0 0;
  /* padding: 0 1rem; */
`;

export const LocationDescription = styled.div`
  color: #101723f2;
  letter-spacing: 0px;
  margin: 0px;
  font-size: 14px;
  line-height: 20px;
  p {
    margin: 0;
  }

  button {
    color: rgb(34, 102, 255);
    padding: 0px;
    border-radius: 2px;
    background: none;
    border: none;
    font-weight: 600;
    cursor: pointer;
    outline: none;
  }
`;

export const LocationDetailSection = styled.div`
  flex-shrink: 0;
  background-color: #fff;
  position: relative;
  padding: 12px 15px;
  margin-bottom: 8px;
  border-radius: 16px;
  cursor: pointer;
  * {
    user-select: none;
  }

  ${({ strips }) =>
    strips &&
    css`
      display: flex;
      flex-direction: column;
      gap: 15px;

      * {
        pointer-events: auto;
      }
    `}
`;

export const LocationDetailSectionStrip = styled.div`
  /* display: flex; */
  position: relative;
  &:not(:first-child):before {
    content: '';
    align-self: flex-start;
    border-bottom: 0.0625rem solid #0f36711a;
    left: 0;
    position: absolute;
    right: -20px;
    background: none;
    height: auto;
    top: -8px;
    margin: 0;
    mask-image: none;
    text-align: justify;
    width: auto;
  }
`;
export const LocationDetailSectionTitle = styled.h3`
  width: 100%;
  margin: 0;
  color: #8e8e93;
  font-size: 0.824rem;
  line-height: 1.5;
  font-weight: 400;
  /* text-transform: capitalize;
  color: rgb(74, 78, 87);
  letter-spacing: 0px;
  margin: 0px;
  font-size: 16px;
  line-height: 24px;
  display: flex;
  align-items: center; */
`;

export const LocationFocusWrapper = styled.div`
  user-select: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 1rem;
  cursor: pointer;
  background: rgb(242, 242, 242);
  border-radius: 10px;
  transition: filter 0.1s;
  justify-content: normal;
  gap: 0.25rem;
  padding: 0px;
  background: transparent;
  border: none;
  cursor: pointer;
`;

export const LocationCollapseSection = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

export const LocationCollapseSectionIns = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 14px;
  letter-spacing: 0px;
  line-height: 1.267em;
  font-weight: 600;
`;

export const Status = styled.p`
  color: ${({ color }) => (color ? color : '#0d0d0d')};
  letter-spacing: 0px;
  margin: 0px;
  font-size: 14px;
  font-weight: 600;
  line-height: 16px;
  display: flex;
  align-items: center;
`;

export const LocationCollapseSectionHead = styled.p`
  color: rgb(74, 78, 87);
  letter-spacing: 0px;
  margin: 0px;
  font-size: 15px;
  line-height: 20px;
  margin-top: -2px;

  display: ${({ expand }) => (!expand ? 'flex' : 'none')};
  align-items: center;
`;

export const LocationCollapseSectionLink = styled.a`
  letter-spacing: 0px;
  margin: 0px;
  font-size: 16px;
  line-height: 24px;
  margin-top: -2px;
  color: #027aff;
  display: flex;
  text-decoration: none;
  align-items: center;
`;

export const CollapseArrow = styled.div`
  display: flex;
  align-items: center;
  transition: transform 0.5s;
  transform: ${({ expand }) => (expand ? 'rotate(-90deg)' : 'rotate(90deg)')};
  position: absolute;
  top: 10px;
  right: 10px;
`;

export const LocationFocusBody = styled.div`
  overflow: hidden;
  user-select: none;
  /* background: rgb(255, 255, 255); */
  margin-top: ${({ expand }) => (expand ? '10px' : 0)};
  opacity: ${({ expand }) => (expand ? 1 : 0)};
  height: ${({ expand }) => (expand ? 'auto' : 0)};
`;

export const LocationHours = styled.div`
  display: flex;
  flex-flow: column;
  width: 100%;
  outline: none;
`;

export const Hour = styled.div`
  display: flex;
  flex-flow: row;
  gap: 2.75rem;
  margin: 4px 0px;
  justify-content: space-between;
  line-height: 1.76em;
  display: flex;
  flex-flow: row wrap;
`;

export const HourLabel = styled.p`
  color: #000;
  letter-spacing: 0px;
  margin: 0px;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
`;

export const HourTime = styled.div`
  display: flex;
  flex-flow: column;

  p {
    display: flex;
    color: #000;
    letter-spacing: 0px;
    margin: 0px;
    font-size: 14px;
    line-height: 20px;
    display: flex;
    align-items: center;

    span {
      text-transform: capitalize;
      color: #000;
      letter-spacing: 0px;
      margin: 0px;
      font-size: 14px;
      line-height: 20px;
      display: flex;
      align-items: center;
      &.separator {
        padding-left: 4px;
        padding-right: 4px;
      }
    }
  }
`;

export const LocationSubHead = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  h2 {
    flex: 1;
    font-weight: 600;
    font-size: 14px;
    line-height: 2.5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    margin: 0;
  }
`;

export const DirectionStopContainer = styled.div`
  display: flex;
  flex-direction: row;
  border-radius: 16px;
  background-color: #fff;
  border: 2px solid
    hsla(
      var(--ue-base-form-elements-color_h, 0),
      var(--ue-base-form-elements-color_s, 0%),
      var(--ue-base-form-elements-color_l, 0%),
      var(--ue-base-form-elements-color_t, 0.07)
    );
  margin-bottom: 5px;
  position: relative;
  margin-left: 30px;
`;

export const DirectionSection = styled.div`
  position: relative;

  ${({ type }) =>
    (type === 'origin' || type === 'middle') &&
    css`
      &:after {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: #0f36711a;
        content: '';
      }
    `}
  .close-icon {
    opacity: 0;
    position: absolute;
    right: 40px;
    width: 20px;
    cursor: pointer;
    height: 20px;
    background-color: #00000014;
    border-radius: 50px;
    -webkit-backdrop-filter: blur(50px);
    backdrop-filter: blur(50px);
    display: flex;
    align-items: center;
    justify-content: center;
    top: 4px;
  }

  &:hover {
    .close-icon {
      opacity: 1;
    }
  }
`;

export const DirectionLabel = styled.label`
  display: flex;
  align-items: center;
  padding: 14px 14px 14px 16px;
  position: relative;

  @media (max-width: 763px) {
    padding: 10px 14px 10px 16px;
  }
`;

export const DirectionInput = styled.input`
  border: 0;
  outline: 0;
  background-color: transparent;
  flex: 1;
  padding: 0;
  font-size: 0.941rem;
  line-height: 1.313;
  width: calc(100% - 48px);
  padding-right: 60px;
`;

export const DirectionPaths = styled.div`
  position: absolute;
  top: 0;
  left: -30px;
  height: 100%;
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
`;

export const DirectionIcon = styled.div`
  position: relative;
  border-radius: 50%;
  width: 20px;
  /* height: 20px; */
  padding: 18px 0 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  @media (max-width: 763px) {
    padding: 14px 0 14px;
  }

  ${({ hsdot }) =>
    hsdot &&
    css`
      &:before {
        position: absolute;
        top: calc(100% - 14px);
        width: 100%;
        height: 25px;
        left: 50%;
        content: '';
        background-image: radial-gradient(rgba(0, 0, 0, 0.1) 2px, transparent 0);
        background-size: 12px 12px;
        background-position: center top;
        background-repeat: repeat-y;
        transform: translate(-50%, 0);
      }
    `}
`;

export const NaviagtionSwitcher = styled.div`
  width: 1.765rem;
  height: 1.765rem;
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 50%;
  display: grid;
  place-items: center;
  background-color: #eeedee;
  cursor: pointer;
  transition: 0.2s all ease-out;

  svg {
    background-color: var(--system-blue);
  }
`;

export const AddStopButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  color: ${({ theme }) => theme.colors.primary};
  font-size: 14px;
  font-weight: 600;
  margin: 4px 0;
  cursor: pointer;
  p {
    margin: 0;
  }
`;

export const DragPanHandle = styled.div`
  position: absolute;
  top: 50%;
  color: #8e8e93;

  right: 14px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
`;

export const ButtonGroupWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin: 6px 0 0 6px;
  padding: 0 15px 15px;
`;

export const RouteInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  max-height: 40px;

  h3 {
    margin: 0;
    font-weight: 600;
    font-size: 18px;
    line-height: 1.2;
  }
  p {
    margin: 0;
    font-size: 0.824em;
    line-height: 1.357;
    color: #3c3c4399;
  }
`;

export const RoutesButtons = styled.div`
  display: flex;
  flex: 1;
  justify-content: center;
  gap: 8px;
`;

export const ShareModal = styled(motion.div)`
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 10;
  width: 100%;
  height: 100%;
  /**/
  background-color: #ffffff26;
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
`;

export const ShareModalIns = styled.div`
  width: 22.5rem;
  background-color: #fff;
  border-radius: 12px;

  box-shadow:
    0 0.156rem 0.156rem rgba(0, 25, 75, 0.0509803922),
    0 0 0.5rem rgba(0, 25, 75, 0.0509803922),
    0 0.313rem 0.313rem rgba(0, 25, 75, 0.0509803922);
`;

export const StepsContainer = styled.div`
  /* padding: 0 20px 20px; */
`;

export const StepsWrapper = styled.ul`
  width: 100%;
  border-radius: 16px;
  line-height: 1.324em;
  position: relative;
  list-style: none;
  margin: 0;
  padding: 12px 0;
  box-sizing: border-box;
  /* &:before {
    content: '';
    position: absolute;
    width: 2px;
    background: #afafaf;
    top: 0;
    left: 14px;
    height: 100%;
    z-index: -1;
  }
  &:after {
    content: '';
    position: absolute;
    width: 2px;
    background: ${({ theme }) => theme.colors.primary};;
    top: 0;
    left: 14px;
    height: ${({ height }) => (height ? height : '0')};
    z-index: -1;
    transition: all 0.2s ease-in-out;
  }
  /* 
  &:after {
    content: '';
    position: absolute;
    width: 4px;
    height: 100%;
    border-radius: 50px;
    background: ${({ theme }) => theme.colors.primary};;
    top: 0;
    transition: all 0.2s ease-in-out;
    left: 16px;
      z-index: -1;
  } */
  /* margin-left: 30px; */
  /* width: calc(100% - 30px); */

  /* &::before {
    position: absolute;
    top:-12px;
    bottom: -12px;
    left: -30px;
    width: 30px;
    content: '';
    background-image: radial-gradient(rgba(0, 0, 0, 0.1) 2px, transparent 0);
    background-size: 12px 12px;
    background-position: center top;
    background-repeat: repeat-y;
  } */
`;

export const StepItem = styled.li`
  --left-dot-color: ${({ active }) =>
    active
      ? 'linear-gradient(to bottom, ${({ theme }) => theme.colors.primary}; 0%, rgba(0, 0, 0, 0) 100%);'
      : '#afafaf'};
  display: flex;
  align-items: center;
  position: relative;
  padding: 12px;
  border-radius: 12px;
  margin-left: 30px;
  cursor: pointer;

  &:not(:last-child) {
    &:before {
      content: '';
      position: absolute;
      height: 100%;
      width: 2px;
      /* background: var(--left-dot-color); */
      background: ${({ completed, theme }) => (completed ? theme.colors.primary : 'var(--left-dot-color)')};
      top: 50%;
      /* bottom: 0; */
      left: -15px;
      margin-left: -1px;
    }
  }
  &:after {
    content: '';
    position: absolute;
    height: 8px;
    width: 8px;
    background: ${({ completed, active, theme }) =>
      completed ? theme.colors.primary : active ? theme.colors.primary : '#afafaf'};
    top: 50%;
    transform: translateY(-50%);
    bottom: 0;
    left: -15px;
    margin-left: -4px;
    border-radius: 50%;
  }
  /* &:before {
    content: '';
    background-image: radial-gradient(var(--left-dot-color) 2px, transparent 0);
    background-size: 12px 12px;
    background-position: center top;
    background-repeat: repeat-y;
    position: absolute;
    top: -10px;
    left: -30px;
    height: 50px;
    width: 30px;
  } */
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
    background: rgba(0, 0, 0, 0.04);
  }
`;

export const StepImage = styled.div`
  width: 30px;
  height: 30px;
  object-fit: contain;

  img {
    width: 30px;
    height: 30px;
    object-fit: contain;
  }
`;

export const StepInfo = styled.div`
  margin-left: 16px;
`;

export const StepTitle = styled.h3`
  font-weight: 600;
  font-size: 0.941rem;
  line-height: 1.235rem;
  margin: 0;
  margin-bottom: 3px;
`;

export const StepDetail = styled.p`
  color: rgb(60 60 67 / 60%);
  font-size: 0.824rem;
  line-height: 1.357;
  margin: 0;
`;

export const StepStartPoint = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  width: 100%;
  margin-bottom: -1px;
  position: relative;
  z-index: 2;
`;

export const StepEndPoint = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  width: 100%;
  margin-top: -1px;
  position: relative;
  z-index: 2;
`;

export const StepHeadImg = styled.div`
  /* width: 30px;
  display: flex;
  align-items: center;
  justify-content: center; */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background-color: rgba(0, 0, 0, 0.04);
  overflow: hidden;
  color: hsla(0, 0%, 33.33%, calc(1 - 0.6));
  border-radius: 12px;
  border: 1px solid hsla(0, 0%, 0%, 0.07);
  img {
    max-width: 33px;
    max-height: 33px;
    width: 100%;
    height: auto;
    color: ${({ theme }) => theme.colors.primary};
    object-fit: contain;
  }
`;
export const StepHeadDetails = styled.div`
  h3 {
    font-weight: 600;
    font-size: 0.941rem;
    line-height: 1.235rem;
    margin: 0;
  }
  p {
    color: rgb(60 60 67 / 60%);
    font-size: 0.824rem;
    line-height: 1.357;
    margin: 0;
  }
`;

export const RouteItem = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

export const RouteImage = styled.div`
  width: 56px;
  height: 56px;
  object-fit: contain;

  img {
    width: 56px;
    height: 56px;
    object-fit: contain;
    /* filter: invert(1); */
  }
`;

export const RoutesInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  margin-left: 12px;
  flex: 1;
  height: 100%;
  gap: 8px;
  /* color: #ffffff; */
`;

export const RouteTitle = styled.h3`
  font-weight: 800;
  font-size: 32px;
  line-height: 1.625rem;
  margin: 0;
`;

export const RouteDetail = styled.p`
  color: rgb(0 0 0 / 80%);
  font-size: 16px;
  line-height: 15px;
  margin: 0;
`;

// export const RouteProgressBar = styled.div`
//   background: #0d0d0d;
//   position: relative;
//   height: 8px;
//   width: 100%;
//   text-align: left;
// `;
// export const RouteProgress = styled.div`
//   position: absolute;
//   height: 100%;
//   transition: width 0.4s ease-in;
//   width: 0%;
//   background-color: rgba(252, 252, 55, 0.8);
//   z-index: 1;
// `;
// export const RouteProgressDotWrapper = styled.div`
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
// `;
// export const RouteProgressDot = styled.div`
//   position: relative;
//   width: 14px;
//   height: 8px;
//   background-color: red;
//   float: left;
//   z-index: 2;
// `;

export const RouteManageActions = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  flex-grow: 1;
  width: 100%;
  margin: 20px 0px 0;

  @media (max-width: 768px) {
    margin: 10px 0px 0;
  }
`;
export const RouteManageInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
`;
export const RouteManageTitle = styled.p`
  color: #4a4e57;
  font-size: 16px;
  margin: 0;
  letter-spacing: 0px;
`;
export const RouteManageDetail = styled.h4`
  color: #000;
  letter-spacing: 0px;
  font-size: 18px;
  line-height: 1.267em;
  font-weight: 500;
  margin: 0;
`;
export const RouteManageBtn = styled.div`
  position: relative;
  display: flex;
  width: 115px;
  height: 50px;
  border-radius: 8px;
  background: hsla(
    var(--ue-option-panel-background-color_h, 0),
    var(--ue-option-panel-background-color_s, 0%),
    calc(var(--ue-option-panel-background-color_l, 100%) - 9%),
    calc(var(--ue-option-panel-background-color_t, 1) - 0.1)
  );
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
  overflow: hidden;

  @media (max-width: 768px) {
    height: 40px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    border-left: 1px solid rgb(209, 209, 209);
    transform: translate(-50%);
    z-index: 1;
  }

  button {
    background: none;
    color: inherit;
    padding: 0.5rem;
    font: inherit;
    cursor: pointer;
    outline: none;
    border: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50%;
    &::first-of-type {
      border-radius: 28px 0 0 28px;
    }
    &::last-of-type {
      border-radius: 0 28px 28px 0;
    }
  }
`;

export const RouteProgressBar = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: fit-content;
  width: 100%;
  width: calc(100% + 20px);
  height: 12px;
  margin-left: -10px;
  margin-right: -10px;
  margin-top: -10px;
  overflow: hidden;
  border-radius: 18px 18px 0 0;
  background: rgb(118 118 128 / 28%);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
`;
export const RouteProgress = styled.div`
  height: 100%;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 0;
  transition: all 0.4s ease-in;
`;

export const LocationCategoryList = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 8px;
`;
export const LocationCategory = styled.span`
  background-color: #0000000f;
  text-transform: capitalize;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.1s;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  word-break: initial;
  padding: 3px 5px !important;
`;

export const SegmentedControl = styled.div`
  display: flex;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: hsla(
    var(--ue-option-panel-background-color_h, 0),
    var(--ue-option-panel-background-color_s, 0%),
    calc(var(--ue-option-panel-background-color_l, 100%) - 9%),
    calc(var(--ue-option-panel-background-color_t, 1) - 0.1)
  );
  height: 35px;
  border-radius: 15px;

  input {
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
    clip: rect(1px, 1px, 1px, 1px);
    white-space: nowrap;

    &:checked + label {
      svg {
        color: ${({ theme }) => theme.colors.primary};
      }
    }

    &:nth-last-of-type(2):checked ~ label:last-of-type::after {
      transform: translateX(-113%);
    }
  }

  label {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    flex: 1 1 0px;
    color: hsla(
      var(--ue-default-font-color_h, 0),
      var(--ue-default-font-color_s, 0%),
      var(--ue-default-font-color_l, 33.33%),
      var(--ue-default-font-color_t, 1)
    );
    position: relative;
    z-index: 2;
    transition: inherit;
    font-size: 0.8125rem;
    font-weight: 500;
    line-height: 1;
    width: 55px;
    height: 35px;

    svg {
    }

    &:last-of-type {
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        top: 3px;
        right: 3px;
        bottom: 3px;
        left: 3px;
        z-index: -2;
        background: ${({ theme }) => theme.colors.primary};
        border-radius: 15px;

        border: 0.5px solid rgba(0, 0, 0, 0.04);
        box-shadow:
          0px 3px 1px rgba(0, 0, 0, 0.04),
          0px 3px 8px rgba(0, 0, 0, 0.12);
        background: hsla(
          var(--ue-secondary-color_h, 0),
          var(--ue-secondary-color_s, 0%),
          var(--ue-secondary-color_l, 100%),
          var(--ue-secondary-color_t, 1)
        );
        box-shadow: 0 1px 3px 0 var(--ue-base-shadow-color, rgba(0, 0, 0, 0.1));
        transition: inherit;
      }
    }
  }
`;

export const Alert = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  width: auto;
  height: 100%;
  z-index: 9999;
  color: #ff543e;
  font-size: 13px;
  font-weight: 600;
  margin: 0 15px 15px;
  line-height: 16px;
  div {
    flex: 0 0 25px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  p {
    margin: 0;
  }
`;

export const MultiRouteInfoWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  padding: 8px;
`;
export const MultiRouteInfoIcon = styled.div`
  flex: 0 0 35px;
  color: ${({ theme }) => theme.colors.primary};
  @keyframes shake {
    0% {
      transform: rotate(0);
    }
    25% {
      transform: rotate(-5deg);
    }
    50% {
      transform: rotate(5deg);
    }
    75% {
      transform: rotate(-5deg);
    }
    100% {
      transform: rotate(0);
    }
  }
`;
export const MultiRouteInfoContent = styled.div`
  h5 {
    font-weight: 600;
    white-space: pre-wrap;
    font-size: 0.941rem;
    line-height: 1.313;
    color: #000;
    margin: 0;
  }
  p {
    font-size: 0.824rem;
    line-height: 1.357;
    color: rgb(60 60 67 / 60%);
    margin: 0;
    margin-top: 2px;
  }
`;

export const Panel = styled.div`
  width: 100%;
  z-index: 98;
  position: fixed;
  top: 0;
  bottom: 0;
  transition: transform 0.3s ease-in-out !important;
  pointer-events: none;
  opacity: 0;
  display: none;
  visibility: hidden;
  background-color: #efefef;
  left: 0;
  /* transform: translateX(calc(-100% - 64px)); */
  box-shadow: 10px 1px 5px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease-in-out !important;
  padding-bottom: 54px;

  ${({ open }) =>
    open &&
    css`
      pointer-events: auto;
      opacity: 1;
      display: block;
      visibility: visible;
    `}
`;
export const PanelInner = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  max-height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
  ${({ center }) =>
    center &&
    css`
      display: flex;
      align-items: center;
      height: 100dvh;
      justify-content: center;
      flex-direction: column;
    `}
`;
export const PanelHeader = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  border-bottom: 1px solid rgb(118 118 128 / 12%);
`;
export const PanelBody = styled.div`
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  padding: 16px;
  ${({ center }) =>
    center &&
    css`
      display: flex;
      align-items: center;
      height: 100dvh;
      justify-content: center;
      flex-direction: column;
    `}
`;

export const SearchArea = styled.div`
  position: relative;
  width: 100%;
  padding: 8px 0;
  margin: 0 16px;
  span {
    color: #252f4a;
    line-height: 1;
    margin-left: 1.25rem;
    transform: translateY(-50%);
    top: 50%;
    position: absolute;
    z-index: 3;
    svg {
      height: 1.35rem;
      width: 1.35rem;
    }
  }
  input {
    display: block;
    appearance: none;
    letter-spacing: 0px;
    transition-property: width, padding, margin;
    transition-duration: 0.5s;
    font-size: 16px;
    position: relative;
    z-index: 2;
    box-sizing: border-box;
    height: 44px;
    width: 100%;
    padding: 0px 1rem;
    background-color: #fff;
    border-radius: 15px;
    color: #000;
    border: 2px solid hsla(0, 0%, 0%, 0.07);
    padding-left: 50px;

    &::placeholder {
      /* color: #708199; */
      font-style: normal;
      font-weight: 500;
    }
    &:hover,
    &:focus {
      outline: none;
    }
  }
`;

export const PanelTitle = styled.div`
  min-height: 44px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
`;

export const ItemCard = styled.div`
  box-shadow:
    0 0 0px #0000,
    0 0 0px #0000,
    0px 1px 2px 0px rgba(16, 24, 40, 0.06),
    0px 1px 3px 0px rgba(16, 24, 40, 0.1);
  background-color: #fff;
  border-radius: 0.75rem;
  display: flex;
  position: relative;
  width: 100%;
  border-radius: 20px;
  background: #fff;
  line-height: 1.324em;
  position: relative;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  text-decoration: none;
  cursor: pointer;
  overflow: hidden;
  padding-right: 0;
  transition: all 0.14s ease-out;
  width: 100%;

  ${({ contact }) =>
    contact &&
    css`
      &:after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: -1px;
        pointer-events: none;
        content: '';
        border: 2px solid rgba(${({ theme }) => theme.colors.primaryRgb}, 0.5);
        border-radius: inherit;
        -webkit-mask-image: linear-gradient(310deg, #000, transparent 50%);
        mask-image: linear-gradient(310deg, #000, transparent 50%);
      }
    `}

  &:not(:last-child) {
    margin-bottom: 10px;
  }
  .image-wrapper {
    /* max-height: 108px; */
    width: 100%;
    object-fit: contain;
    -m-object-fit: contain;
    overflow: hidden;
    border-radius: 12px;
    margin-top: 12px;
    img {
      border-radius: 12px;
      width: 100%;
      height: auto;
    }
  }
  .item-line {
    line-height: 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 2px 0;
    overflow: hidden;
    font-weight: 500;
    color: rgb(60 60 67 / 60%);
    gap: 3px;
  }
  .offer-cnt {
    display: flex;
    flex-direction: column;
    gap: 2px;
    .tags {
      display: -ms-flexbox;
      display: flex;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
      margin-bottom: 10px;
      span {
        font-size: 10px;
        font-weight: 500;
        letter-spacing: 0.5px;
        background-color: rgba(29, 161, 242, 0.14);
        border-radius: 5px;
        line-height: normal;
        padding: 2px 5px;
        color: #1da1f2;
        margin-right: 10px;
      }
    }
    h3 {
      font-weight: 600;
      font-size: 18px;
      line-height: 21px;
      white-space: normal;
      color: #000;
      margin: 0;
    }
    p {
      color: rgb(60 60 67 / 60%);
      letter-spacing: 0px;
      margin: 0px;
      font-size: 14px;
      line-height: 19px;
      margin: 0;
      button {
        color: rgb(34, 102, 255);
        padding: 0px;
        border-radius: 2px;
        background: none;
        border: none;
        font-weight: 600;
        cursor: pointer;
        outline: none;
      }
    }
    .offer-store {
      margin-bottom: 0;
      color: #8e8f8f;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .item-body {
    padding: 12px 16px 12px 12px;
    // padding: 12px 16px 12px 0;
    // margin-left: 70px;
    border-top: 1px solid #eeeeee;
    transition: all 0.3s ease 0s;

    p {
      font-size: 14px;
      margin-bottom: 10px;
    }
  }
  .btn-wrapper {
    gap: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .docs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    .docs-item {
      padding: 6px 8px;
      background: #e9e9e9;
      border-radius: 8px;
      line-height: 18px;
      font-size: 13px;
      font-weight: 500;
    }
  }

  .times {
    display: flex;
    align-items: center;
    font-size: 14px;
    background-color: #eeeeee;
    padding: 6px;
    border-radius: 6px;
    margin-top: 5px;

    svg {
      width: 16px;
      height: 16px;
      color: #000;
    }
    .badge {
      border-radius: 5px !important;
      padding-top: 5px;
      margin: 0 5px;
    }
    .badge-danger {
      color: #fff;
      background-color: #dc3545;
    }

    .badge-success {
      color: #fff;
      background-color: #28a745;
    }

    .badge-pill {
      padding-right: 0.6em;
      padding-left: 0.6em;
    }
    // .badge-danger {
    //     background-color: #f8d7da!important;
    //     color: #dc3545!important;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center ;

    // }
  }
`;

export const TabArea = styled.div`
  border-bottom: 1px solid #e3e3e3;
  background-color: #fff;
  .nav-tabs {
    border-bottom: 2px solid #e3e3e3;
    background-color: rgba(0, 0, 0, 0.07);
    .nav-link {
      position: relative;
      font-size: 13px;
      font-weight: 600;
      color: #2e2e2e;
      &:hover {
        border-color: transparent;
      }
      &.active,
      &.active:hover {
        border-width: 0;
        border: none;
        color: ${({ theme }) => theme.colors.primary};
        background: transparent;
        box-shadow: none;
        border-color: transparent;
        &::after {
          transform: scale(1);
          background: ${({ theme }) => theme.colors.primary} none repeat scroll 0% 0%;
          color: #fff;
        }
      }
      &::after {
        content: '';
        background: ${({ theme }) => theme.colors.primary};
        height: 2px;
        position: absolute;
        width: 100%;
        left: 0px;
        bottom: -3px;
        transition: all 250ms ease 0s;
        transform: scale(0);
      }
    }
  }
  .tab-content {
    padding: 12px;
  }
`;

export const ItemCardIns = styled.div`
  position: relative;
  height: 100%;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
`;
export const DepartmentDetails = styled.p`
  color: rgb(60 60 67 / 80%);
  letter-spacing: 0px;
  margin: 0px;
  font-size: 14px;
  line-height: 19px;
  text-align: left;
  margin: 0;
  button {
    color: rgb(34, 102, 255);
    padding: 0px;
    border-radius: 2px;
    background: none;
    border: none;
    font-weight: 600;
    cursor: pointer;
    outline: none;
    text-decoration: none;
    font-size: 14px;
  }
`;

export const Flex = styled.div`
  display: flex;
  align-items: ${({ align = 'stretch' }) => align};
  justify-content: ${({ justify = 'flex-start' }) => justify};
  gap: ${({ gap = '0' }) => gap};
  flex-direction: ${({ dir = 'row' }) => dir};
  width: 100%;
`;
export const ContactFormWrapper = styled.div`
  position: relative;
  padding: 10px;
  transition: all 0.3s ease;
`;
export const ContactFormTitle = styled.h3`
  transition: all 0.3s ease;
  font-weight: 700;
  color: #000;
  font-size: 20px;
`;
export const ContactFormDescription = styled.p`
  color: #4b5563;
  margin: 0;
  font-weight: 400;
  font-size: 16px;
  line-height: 140%;
`;
export const ContactForms = styled.div`
  position: relative;
  margin-top: 10px;
`;
export const ContactFormRow = styled.div`
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));

  & > * {
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    padding-right: calc(var(--bs-gutter-x) * 0.5);
    padding-left: calc(var(--bs-gutter-x) * 0.5);
    margin-top: var(--bs-gutter-y);
  }
`;
export const ContactFormItem = styled.div`
  position: relative;
  flex: 0 0 auto;
  width: 100%;
  &:not(:last-child) {
    margin-bottom: 15px;
  }
`;
export const ContactFormSingleItem = styled.div`
  position: relative;

  label {
    font-size: 13px;
    font-weight: 500;
    line-height: 24px;
    color: #101010;
    margin-bottom: 1px;
    span {
      font-size: 10px;
      color: red;
    }
  }

  input,
  textarea {
    display: block;
    appearance: none;
    letter-spacing: 0px;
    transition-property: width, padding, margin;
    transition-duration: 0.5s;
    font-size: 14px;
    margin: 0;
    position: relative;
    z-index: 2;
    box-sizing: border-box;
    /* min-height: 44px; */
    width: 100%;
    padding: 10px 14px;
    border-radius: 15px;
    color: #000;
    border: none;
    background-color: #0000000d;
    border: 2px solid transparent;

    ${({ error }) =>
      error &&
      css`
        border: 2px solid #ff7c7c;
      `};
  }

  textarea {
    min-height: 120px;
  }

  button {
    min-width: 180px;
  }

  .error {
    color: #ff7c7c;
    font-size: 12px;
    font-weight: 500;
    line-height: 10px;
    margin-top: 5px;
    display: block;
    margin-left: 3px;
  }
`;

export const ContactHeader = styled.div`
  width: 100%;
  text-align: left;
  h4 {
    background: linear-gradient(271.72deg, #27713b 7.09%, ${({ theme }) => theme.colors.primary}; 96.61%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: inherit;
    font-size: 28px;
    line-height: 28px;
    font-weight: 800;
    margin-bottom: 8px;
  }
  p {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    margin-bottom: 0px;
    span {
      font-size: 14px;
      font-weight: 600;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 1.235rem;
      margin: 0 0 14px 0;
    }
  }
`;

export const SuccessModal = styled.div`
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  border-radius: 0.75rem;

  inset: 2px;
  background-color: #fff;
  z-index: 11;

  .message {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 0px;
  }

  .sub-message {
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(#000, 0.7);
    font-weight: 400;
    font-size: 13px;
    margin-bottom: 15px;
  }

  button {
    &.button-success {
      font-size: 16px;
      border-radius: 50px;
      color: #fff;
      background-color: #2ea951;
      border: 1px solid #2ea951;
      outline: none;
      font-weight: 500;
    }
  }

  .loader {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 6px solid #2ea950;
    position: relative;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin: 0 auto;
    animation: fadeIn 1.5s linear;
  }

  .checkmark {
    &::after {
      content: '';
      border-right: 6px solid #2ea950;
      border-top: 6px solid #2ea950;
      height: 43px;
      width: 18px;
      transform: scaleX(-1) rotate(135deg);
      animation: checkmark 2s ease;
      position: absolute;
      top: 52px;
      left: 23px;
      margin-top: -3px;
      transform-origin: left top;
      border-radius: 4px;
    }
  }

  // KeyFrames
  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes checkmark {
    0% {
      height: 0;
      width: 0;
      opacity: 1;
    }

    50% {
      height: 0;
      width: 0;
      opacity: 1;
    }

    60% {
      height: 0;
      width: 18px;
      opacity: 1;
    }

    70% {
      height: 35px;
      width: 18px;
      opacity: 1;
    }

    80% {
      height: 43px;
      width: 18px;
      opacity: 1;
    }

    100% {
      height: 43px;
      width: 18px;
      opacity: 1;
    }
  }
`;

export const QuestionNairWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(215, 255, 215, 0.6) 0%, rgba(234, 218, 255, 0) 102.67%);
`;
export const QuestionNairHeader = styled.div`
  width: 376px;
  margin-bottom: 20px;
  padding: 0px 12px 0px 12px;
  font-size: 35px;
  font-weight: 600;
  line-height: 40px;
  text-align: left;
`;
export const QuestionNairBody = styled.div`
  display: flex;
  padding: 1.5rem 1.25rem;
  flex-direction: column;
  /* max-width: 320px; */
  max-width: 520px;
  max-height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1;

  @media (min-width: 520px) {
    margin: auto;
  }
`;
export const QuestionNairFooter = styled.div`
  display: block;
  background-color: #fff;
  background-color: #fff;

  ${ButtonGroup} {
    padding: 0.9375rem 1.25rem 0.9375rem 1.25rem;
  }
  /* display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  margin-top: 24px;
  @media only screen and (min-width: 1281px), only screen and (min-width: 901px) and (max-width: 1280px) {
    flex-direction: row;
    width: 376px;
  } */
`;

export const QuestionNairProgress = styled.div`
  display: flex;
  width: 100%;
`;
export const QuestionNairProgressIndicator = styled.div`
  flex: 1;
  height: 7px;
  border-radius: 100px;
  background-color: hsla(0, 0%, 52.2%, 0.1);
  margin-right: 7px;
  margin-bottom: 25px;
  &.stepper-cell-filled {
    /* background-color: #00597f; */
    background: ${({ theme }) => theme.colors.primary};
  }
  /* height: inherit;
  background-color: ${({ theme }) => theme.colors.primary};;
  transition: transform 500ms cubic-bezier(0.25, 0.1, 0.25, 1);
  transform: scaleX(0);
  transform-origin: 0 50%;

  transform: scaleX(${({ progress }) => progress}); */
`;
export const Question = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.00833em;
  line-height: 1.5rem;
  max-width: 420px;
  margin-bottom: 1.15rem;
`;

export const Answers = styled.div`
  list-style-type: none;
  padding: calc(0.125rem + 0.0625rem);
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
`;

export const SingleAnswer = styled.label`
  padding: 1rem 1rem;
  width: 100%;
  gap: 0.75rem;
  flex-direction: row;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  background: #00000010;
  border-radius: 0.75rem;
  transition: border-color 0.1s cubic-bezier (0.19, 0.91, 0.38, 1);
  font-size: 14px;
  line-height: 16px;
  &:has(input:checked) {
    box-shadow:
      0 -2px 20px #0000001a,
      0 0 12px #0000001a,
      0 1px 3px 0 #00000012,
      0 0 0 2px ${({ theme }) => theme.colors.primary}; inset;
  }
`;

export const SingleAnswerInput = styled.input`
  appearance: none;
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  border-color: rgb(97 97 97);
  background-color: rgb(250 250 250);
  border: 1.5px solid rgb(168, 168, 168);
  border-radius: 6px;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    bottom: 8px;
    left: 3px;
    display: block;
    width: 6px;
    height: 11px;
    border-bottom: 2.5px solid #fff;
    border-right: 2.5px solid #fff;
    transform: rotate(40deg);

    transform-origin: bottom left;
  }

  &:not(:checked)::after {
    width: 0;
    height: 0;
    visibility: hidden;
  }
  &:checked {
    background-color: ${({ theme }) => theme.colors.primary};
    border-color: ${({ theme }) => theme.colors.primary};
    &::after {
      visibility: visible;
      animation: check 0.3s cubic-bezier(0.19, 0.91, 0.38, 1);
    }
  }
  @keyframes check {
    0% {
      width: 0px;
      height: 0px;
      border-right: 2.5px solid transparent;
    }

    10% {
      width: 0px;
      height: 0px;
      border-right: 2.5px solid transparent;
    }

    30% {
      width: 6px;
      height: 0px;
      border-right: 2.5px solid transparent;
    }

    40% {
      width: 6px;
      height: 0px;
      border-right: 2.5px solid transparent;
    }

    50% {
      width: 6px;
      height: 11px;
      border-right: 2.5px solid #fff;
    }
  }

  ${({ isRadio }) =>
    isRadio &&
    css`
      border-radius: 50px;
      &:after {
        bottom: 3px;
        border: none;
        width: 12px;
        height: 12px;
        background-color: #fff;
        transform: unset;
        border-radius: 50px;
        transform-origin: center;
        animation: check2 0.3s cubic-bezier(0.19, 0.91, 0.38, 1);
      }

      @keyframes check2 {
        0% {
          transform: scale(0);
        }

        10% {
          transform: scale(0);
        }

        30% {
          transform: scale(0.5);
        }

        40% {
          transform: scale(0.5);
        }

        50% {
          transform: scale(1);
        }
      }
    `}
`;

export const QuestionnairBg = styled.div`
  position: absolute;
  inset: 0px;

  z-index: -1;
  * {
    z-index: -1;
  }
`;

export const QuestionnairBgIns = styled.div`
  background: #fff;
  container-name: animated-bg;
  container-type: inline-size;
  height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
`;

export const QuestionnairBgGradient = styled.div`
  position: absolute;
  top: 0;
  height: 100%;
  left: 0;
  width: 100%;
  z-index: 3;
  svg {
    overflow-clip-margin: content-box;
    overflow: hidden;
  }
`;

export const QuestionnairBgGradientAnimated = styled.div`
  animation: qBdPi9 1.5s ease-in-out forwards;
  aspect-ratio: 1 / 1;
  filter: blur(50px);
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
  position: absolute;
  top: 50%;

  @keyframes qBdPi9 {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
`;
export const QuestionnairBgGradientAnimatedIns = styled.div`
  animation: BQaDv9 3s ease-in-out infinite alternate;
  background: rgba(233, 240, 255, 0.5);
  background-size: cover;
  clip-path: url(#cloud-center-clip);
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  will-change: transform;

  @keyframes BQaDv9 {
    0% {
      opacity: 0.9;
    }
    100% {
      opacity: 1;
      transform: scale(1.1) skewX(5deg) rotate(10deg) skewY(-5deg);
    }
  }
`;

export const BgLines = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100vh;
  margin: auto;
  width: 99.9%;
  background-color: #fff;
`;

export const BgSingleLine = styled.div`
  position: absolute;
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
  background: hsla(0, 0%, 60%, 0.1);
  overflow: hidden;

  &::after {
    content: '';
    display: block;
    position: absolute;
    height: 15vh;
    width: 100%;
    top: -50%;
    left: 0;
    /* background: -webkit-gradient(
      linear,
      left top,
      left bottom,
      from(rgba(255, 255, 255, 0)),
      color-stop(75%, #ffffff),
      to(#ffffff)
    ); */
    background: linear-gradient(
      0deg,
      rgba(174, 255, 132, 0.43),
      rgba(167, 217, 254, 0) 0.01%,
      #a9efff 22.4%,
      #b2ffe6 65.62%,
      rgba(178, 255, 230, 0) 94.27%
    );
    -webkit-animation: run 7s 0s infinite;
    animation: run 7s 0s infinite;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-timing-function: cubic-bezier(0.4, 0.26, 0, 0.97);
    animation-timing-function: cubic-bezier(0.4, 0.26, 0, 0.97);
  }

  &:nth-child(1) {
    margin-left: -10%;
  }
  &:nth-child(2) {
    margin-left: -20%;
  }
  &:nth-child(3) {
    margin-left: -30%;
  }
  &:nth-child(4) {
    margin-left: -40%;
  }
  &:nth-child(5) {
    margin-left: -50%;
  }
  &:nth-child(6) {
    margin-left: 10%;
  }
  &:nth-child(7) {
    margin-left: 20%;
  }
  &:nth-child(8) {
    margin-left: 30%;
  }
  &:nth-child(9) {
    margin-left: 40%;
  }
  &:nth-child(10) {
    margin-left: 49.9%;
  }

  &:nth-child(1)::after {
    -webkit-animation-delay: 1.5s;
    animation-delay: 1.5s;
  }
  &:nth-child(2)::after {
    -webkit-animation-delay: 2s;
    animation-delay: 2s;
  }
  &:nth-child(3)::after {
    -webkit-animation-delay: 1.8s;
    animation-delay: 1.8s;
  }
  &:nth-child(4)::after {
    -webkit-animation-delay: 2.5s;
    animation-delay: 2.5s;
  }
  &:nth-child(5)::after {
    -webkit-animation-delay: 2.1s;
    animation-delay: 2.1s;
  }
  &:nth-child(6)::after {
    -webkit-animation-delay: 2.8s;
    animation-delay: 2.8s;
  }
  &:nth-child(7)::after {
    -webkit-animation-delay: 1.3s;
    animation-delay: 1.3s;
  }
  &:nth-child(8)::after {
    -webkit-animation-delay: 2.9s;
    animation-delay: 2.9s;
  }
  &:nth-child(9)::after {
    -webkit-animation-delay: 2.4s;
    animation-delay: 2.4s;
  }
  &:nth-child(10)::after {
    -webkit-animation-delay: 3.1s;
    animation-delay: 3.1s;
  }

  @-webkit-keyframes run {
    0% {
      top: -50%;
    }
    100% {
      top: 110%;
    }
  }

  @keyframes run {
    0% {
      top: -50%;
    }
    100% {
      top: 110%;
    }
  }
`;

export const LoaderImage = styled.img`
  width: 100%;
  height: auto;
  transform: scale(1.5);
  filter: invert(1);
`;

export const LoaderWrapper = styled.div`
  width: 120px;
  background-color: #fff;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
`;

export const SkipButton = styled.div`
  padding: 20px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.5);
`;

export const PrefrenceButton = styled.div`
  position: fixed;
  z-index: 3;
  bottom: 75px;
  right: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border: none;
  color: #fff;
  font-size: 20px;
  border-radius: 50%;
  text-decoration: none;
  background-color: #4b40cc;
  box-shadow:
    0px 2px 4px rgba(0, 0, 0, 0.15),
    0px 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.15s ease-in-out;
  background-color: #89d8d3;
  background-image: linear-gradient(135deg, #89d8d3 0%, #03c8a8 74%);
  svg {
    transition: all 0.2s ease-in-out;
  }
`;

export const LocationEvent = styled.div``;
export const LocationEventHead = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  /* display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: 1fr;
  grid-column-gap: 12px;
  grid-row-gap: 0px; */
  overflow: hidden;
  scrollbar-width: none;
  /* */
  padding: 18px 0;
`;
export const EventDateGroup = styled.div`
  /* border:1px solid rgb(255 111 30 / 20%); */
  border-radius: 12px;
  max-height: 75px;
  min-height: 65px;
  min-width: 60px;
  background-color: rgba(0, 0, 0, 0.1);

  ${({ active }) =>
    active &&
    css`
      background-color: ${({ theme }) => theme.colors.primary};;
      box-shadow:
        0 -2px 20px #0000001a,
        0 0 12px #0000001a,
        0 1px 3px 0 #00000012,
        0 0 0 2px ${({ theme }) => theme.colors.primary}; inset;

      ${EventDate} {
        color: #fff;

      }
      ${EventMonth} {
        color: #fff;
      }
    `}

  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.3;
      pointer-events: none;
    `}
`;
export const EventMonth = styled.span`
  justify-content: center;
  align-items: center;
  display: flex;
  color: rgb(142, 142, 147);
  font-weight: 400;
  font-size: 12px;
  line-height: 1.125rem;
  padding: 0.5rem 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.2px;
`;
export const EventDate = styled.span`
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 34px;
  display: flex;

  color: rgb(74, 78, 87);
  letter-spacing: -0.025em;
  font-weight: 600;
  font-size: 24px;
  line-height: 1.8125rem;
`;

export const LocationEventBody = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
export const TimelineContainer = styled.ul`
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  border-left: 2px solid rgba(0, 0, 0, 0.15);

  ${({ noLine }) =>
    noLine &&
    css`
      border-left: none;
    `}/* &::before {
    content: '';
    width: 2px;
    background-color: #d3dae0;
    position: absolute;
    top: 0;
    bottom: 0;
  } */
`;
export const TimelineItem = styled.li`
  display: flex;
  margin-bottom: 12px;
  gap: 8px;
  flex-direction: column;
  align-items: flex-start;
`;
export const TimelineTime = styled.div`
  flex: 1;
  color: inherit;
  display: flex;
  align-items: center;
  white-space: nowrap;
  position: relative;
  line-height: 30px;
  display: flex;
  text-align: left;
  flex-direction: column;

  span {
    text-align: left;
    font-size: 14px;
    font-weight: 600;
    color: ${({ theme }) => theme.colors.primary};
    margin-left: 16px;
  }

  &::before {
    content: '';
    height: 30px;
    width: 6px;
    background-color: ${({ theme }) => theme.colors.primary};
    border-radius: 0 8px 8px 0;
    margin-right: 3px;
    position: absolute;
    z-index: 2;
    left: 0;
  }
`;
export const TimelineContent = styled.div`
  padding: 0;
  border-radius: 16px;
  padding: 0 16px 0;
  width: 100%;
`;

// export const TimelineContainer = styled.div`
//   position: relative;

//   &::before {
//     content: '';
//     position: absolute;
//     left: 51px;
//     width: 3px;
//     top: 0;
//     bottom: 0;
//     background-color: #f1f1f4;
//   }
// `;

// export const TimelineItem = styled.div`
//   display: flex;
//   align-items: flex-start;
//   position: relative;
//   margin-bottom: 1.7rem;
// `;

// export const TimelineLabel = styled.div`
//   width: 50px;
//   flex-shrink: 0;
//   position: relative;
//   color: #252f4a !important;
//   font-weight: 700 !important;
//   font-size: 13px;

//   &:before {
//     content: '';
//     position: absolute;
//     left: 51px;
//     width: 3px;
//     top: 0;
//     bottom: 0;
//     background-color: #f1f1f4;
//   }
// `;

// export const TimelineBadge = styled.div`
//   flex-shrink: 0;
//   background-color: #ffffff;
//   width: 14px;
//   height: 14px;
//   border-radius: 50px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   z-index: 1;
//   position: relative;
//   margin-top: 1px;
//   margin-left: -0.4rem;
//   padding: 3px !important;
// `;

// export const TimelineContent = styled.div`
//   flex-grow: 1;
//   color: #99a1b7 !important;
//   padding-left: 0.75rem !important;
// `;

export const MapAttrbutes = styled.div`
  position: absolute;
  z-index: 50;
  pointer-events: none;
  left: 0;
  bottom: 0;

  @media (max-width: 734px) {
    top: 0;
    bottom: auto;
  }
  /* bottom: ${({ top }) => top}; */
`;
export const MapAttrbuteControl = styled.div`
  margin-left: 12px;
  margin-bottom: 12px;
  float: left;
  clear: both;
  position: relative;
  z-index: 80;
  @media (max-width: 734px) {
    margin-top: 12px;
    margin-bottom: 0;
  }
`;
export const MapAttrbuteLogo = styled.div`
  width: 73px;
  height: 12px;
  background-size: cover;
`;
