import {
  ButtonGroupWrapper,
  MultiRouteInfoContent,
  MultiRouteInfoIcon,
  MultiRouteInfoWrapper,
  RouteDetail,
  RouteImage,
  RouteItem,
  RouteManageActions,
  RouteManageBtn,
  RouteManageDetail,
  RouteManageInfo,
  RouteManageTitle,
  RouteProgress,
  RouteProgressBar,
  RoutesButtons,
  RoutesInfo,
  RouteTitle,
  StateCard,
  StateCardBody,
} from '../MapView/index.styled';
import Button from '@/components/shared/button/button';
import Icon from '@/components/shared/Icon';
import { useMap } from '../../context/MapContext';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { calculateWalkingTime, directionImage, limitedDirectionContent, metersToFeet } from '../../shared/utils';
import { isMobile } from 'react-device-detect';
import { navigateOnMapSteps } from '../../shared/common';
import introJs from 'intro.js';
import { trackEvent } from '../../shared/analyticsService';
import config from '../../config/envConfig';

const NavigateOnMap = ({ stopNavigation, segments,jumpTo,initialSegment=0,initialStep=0 }) => {
  const { map,isStarted } = useMap();
  const [nowAt, setNowAt] = useState(false);
  const [currentSegment, setCurrentSegment] = useState(initialSegment);
  const [allSegments, setAllSegments] = useState([]);
  const [reachedEnd, setReachedEnd] = useState(false);
  const overRideListenerRef = React.useRef(false);
  const jumpedRef = React.useRef(false);
  useEffect(() => {
    if (allSegments?.length === 0) return;
    setNowAt({ ...allSegments[0]?.step, orderIndex: 0 });
    map.referance.routeController.showStep(allSegments[0]?.step);
  }, [allSegments, map]);

  useEffect(() => {
    let data = segments[currentSegment];
    if (!data) return;
    if (jumpTo && isMobile && !jumpedRef.current) {
      setTimeout(() => {
        setNowAt({ ...jumpTo, orderIndex: initialStep });
        map.referance.routeController.showStep(jumpTo);
        jumpedRef.current = true;
      }, 100);
      // map.referance.routeController.showStep(jumpTo);
    }
    setAllSegments(
      data.steps.map((it, index) => {
        return { segmentIndex: currentSegment, stepIndex: index, step: it };
      })
    );
  }, [segments, currentSegment, jumpTo]);
  useEffect(() => {
    trackEvent('navigate_on_map');
    return ()=>{
      jumpedRef.current=false;
    }
  },[])

  console.log({nowAt})
  useEffect(() => {
    // if (map.referance) {
    //   map.referance.eventsHandler.on('stepLoad', (step) => {
    //     if (overRideListenerRef.current) return;
    //     const data = {};
    //     allSegments.forEach((it) => {
    //       data[it.stepIndex] = it.step;
    //     });
    //     if (step?.step?.orderIndex !== undefined) {
    //       console.log('Step changed', step);
    //       // setNowAt(data[step.step.orderIndex + 1] || false);
    //     }
    //   });
    //   map.referance.eventsHandler.on('walkthroughEnd', () => {
    //     if (overRideListenerRef.current) return;
    //     console.log('Walthrough ended', nowAt);
    //   });
    // }
    // return () => {
    //   if (map.referance) {
    //     map.referance.eventsHandler.off('stepLoad');
    //     map.referance.eventsHandler.off('walkthroughEnd');
    //   }
    //   overRideListenerRef.current = false;
    // };
  }, [allSegments, map, nowAt, segments]);

  const goNext = useCallback(() => {
    overRideListenerRef.current = true;
    const stop = allSegments[nowAt ? nowAt.orderIndex + 1 : 0];
    const step = stop?.step;
    if (step) {
      // console.log('goNext', { step, stop });
      setNowAt({ ...step, orderIndex: stop.stepIndex });
      map.referance.routeController.showStep(step);
    }
  }, [allSegments, map, nowAt]);
  const goToNextSegment = useCallback(() => {
    if (currentSegment < segments.length - 1) {
      setCurrentSegment(currentSegment + 1);
    }
  }, [currentSegment, segments]);

  const goPrevious = useCallback(() => {
    overRideListenerRef.current = true;
    const stop = allSegments[nowAt ? nowAt.orderIndex - 1 : 0];
    const step = stop?.step;
    if (step) {
      // console.log('goPrevious', { step, stop });
      setNowAt({ ...step, orderIndex: stop.stepIndex });
      map.referance.routeController.showStep(step);
    }
  }, [allSegments, map, nowAt]);
  const calculatePercentage = (reachedOrder, totalItems) => {
    if (totalItems === 0) return 0; // Avoid division by zero
    if (reachedOrder === 0) return 0; // Zero progress for the first item
    return ((reachedOrder + 1) / totalItems) * 100; // +1 because order starts from 0
  };
  // Function to calculate remaining distance
  const calculateRemainingDistance = useCallback((stepIndex, route) => {
    return route
      .filter((item) => item.stepIndex > stepIndex) // Filter steps from the given stepIndex onwards
      .reduce((total, item) => total + item.step.distance, 0); // Sum up the distances
  }, []);

  const percentage = useMemo(
    () => calculatePercentage(nowAt?.orderIndex || 0, allSegments.length),
    [allSegments, nowAt]
  );
  const remainingDistance = useMemo(
    () => calculateRemainingDistance(nowAt?.orderIndex || 0, allSegments),
    [allSegments, calculateRemainingDistance, nowAt]
  );

  const { action, direction, distance, ...rest } = useMemo(
    () => (nowAt ? allSegments[nowAt.orderIndex + 1]?.step || {} : {}),
    [allSegments, nowAt]
  );
  const prevStep = useMemo(() => (nowAt ? allSegments[nowAt.orderIndex]?.step || null : null), [allSegments, nowAt]);

  useEffect(() => {
    if ((allSegments || []).length) {
      if (nowAt?.orderIndex === allSegments.length - 1) {
        setReachedEnd(nowAt?.referenceLocation || false);
      }else{
        setReachedEnd(false);
      }
    } else {
      setReachedEnd(false);
    }
  }, [nowAt, allSegments]);
  useEffect(() => {
    if (!config.TUTORIAL || localStorage.getItem('tourCompletedNavigateOnMap')) return;
    const intro = introJs();
    const steps = navigateOnMapSteps.map((step) => ({
      element: document.querySelector(step.target),
      intro: step.content,
      // position: step.position || 'bottom',
    }));

    intro.setOptions({
      steps: steps,
      showBullets: true,
      exitOnOverlayClick: false,
      keyboardNavigation: false,
      doneLabel: 'Finish',
      nextLabel: 'Next',
      prevLabel: 'Previous',
      hidePrev: true,
    });

    intro.onexit(() => {
      localStorage.setItem('tourCompletedNavigateOnMap', true);
    });

    setTimeout(() => {
      intro.start();
    }, 100);

    return () => {
      intro.exit();
    };
  }, []);

  if (reachedEnd) {
    const { floor, name } = reachedEnd || {};
    const haveNext = currentSegment < segments.length - 1;
    return (
      <StateCard
        startNavigation={isStarted}
        fullRadius
        style={{
          position: "fixed",
          bottom: "0",
          left: "0",
          right: "0",
          borderRadius: "18px 18px 0 0"
          // justifyContent: 'flex-end',
          // padding: '0px',
          // marginBlockStart: '15px',
          // backgroundColor: '#fff',
          // position: 'absolute',
          // bottom: '20px',
          // left: '20px',
          // right: '20px',
          // width: 'calc(100% - 40px)',
        }}
      >
        <StateCardBody style={{ padding: '0' }}>
          <MultiRouteInfoWrapper>
            <MultiRouteInfoIcon>
              <Icon icon="marker-pin-03" size="40" />
            </MultiRouteInfoIcon>
            <MultiRouteInfoContent>
              <h5>You have Arrived at your Stop.</h5>
              <p>
                {name}, {floor?.shortName}
              </p>
            </MultiRouteInfoContent>
          </MultiRouteInfoWrapper>
        </StateCardBody>
        <ButtonGroupWrapper
          style={{
            borderTop: '2px solid #e7e7e7cc',
            padding: '10px',
            alignItems: 'flex-end',
          }}
        >
          <RoutesButtons
            style={{
              justifyContent: 'flex-end',
            }}
          >
            <Button onClick={stopNavigation} type="button" variant="danger">
              End Navigation
            </Button>
            {haveNext && (
              <Button
                onClick={() => {
                  goToNextSegment();
                  setReachedEnd(false);
                }}
                type="button"
                variant="primary"
              >
                Continue
              </Button>
            )}
          </RoutesButtons>
        </ButtonGroupWrapper>
      </StateCard>
    );
  }
  return (
    <>
      <StateCard startNavigation={isStarted} style={{
        borderRadius: screen.width < 768 ? '0 0 18px 18px' : '18px',
        // backgroundColor: '#000',
        backgroundColor: '#21409A',
        color: '#fff',
      }}>
        <StateCardBody
        style={{padding: '0 18px 15px'}}
        >
          <RouteItem>
            <RouteImage>
              <img
                style={{ filter: 'invert(1)' }}
                src={directionImage({ action, direction, distance, ...rest })}
                alt=""
              />
            </RouteImage>
            <RoutesInfo 
            style={{
              justifyContent: !distance && 'center',
            }}>
              {distance ? <RouteTitle>{Math.ceil(metersToFeet(distance))}ft</RouteTitle> : ''}
              {/* {action && direction && ( */}
              <RouteDetail
                style={{
                  color: '#fff',
                }}
              >
                {/* {action} {direction !== 'None' ? `${direction}` : ''} */}
                {limitedDirectionContent({ action, direction, distance, ...rest, prevStep })}
              </RouteDetail>
              {/* )} */}
              {/* <RouteProgressBar>
              <RouteProgress style={{ width: '30%' }}  />
              <RouteProgressDotWrapper>
              {[1, 2, 3, 4].map((i) => (
                <RouteProgressDot  key={i} />
              ))}
              </RouteProgressDotWrapper>
            </RouteProgressBar> */}
            </RoutesInfo>
          </RouteItem>
        </StateCardBody>
      </StateCard>

      <StateCard startNavigation={isStarted} fullRadius style={{ padding: '0px', backgroundColor: '#fff' }}>
        <StateCardBody style={{ padding: '10px' }}>
          <RouteProgressBar>
            <RouteProgress style={{ width: `${percentage || 0}%` }} />
          </RouteProgressBar>
          <RouteManageActions>
            <RouteManageInfo>
              <RouteManageTitle>Time to Destination</RouteManageTitle>
              <RouteManageDetail>{calculateWalkingTime(remainingDistance)}</RouteManageDetail>
            </RouteManageInfo>
            <RouteManageBtn>
              <button className="navigate-step2" disabled={percentage === 0} onClick={goPrevious}>
                <Icon icon="chevron-left" />
              </button>
              <button className="navigate-step1" disabled={percentage === 100} onClick={goNext}>
                <Icon icon="chevron-right" />
              </button>
            </RouteManageBtn>
          </RouteManageActions>
        </StateCardBody>
        <ButtonGroupWrapper
          style={{
            borderTop: '2px solid #e7e7e7cc',
            padding: '10px',
            alignItems: 'flex-end',
          }}
        >
          <RoutesButtons
            style={{
              justifyContent: 'flex-end',
            }}
          >
            {currentSegment < segments.length - 1 ? (
              <Button onClick={goToNextSegment} type="button" variant="white">
                Next Stop
              </Button>
            ) : (
              ''
            )}
            <Button className="navigate-step3" onClick={stopNavigation} type="button" variant="danger">
              End Navigation
            </Button>
          </RoutesButtons>
        </ButtonGroupWrapper>
      </StateCard>
    </>
  );
};

export default React.memo(NavigateOnMap);
