import styled from 'styled-components';

export const EmptyCardWrapper = styled.div`
  width: 100%;
  height: calc(100dvh - 64px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
`;
export const EmptyCardInner = styled.div`
  width: 100%;
  border-radius: 20px;
  background: rgb(255, 255, 255);
  line-height: 1.324em;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 50px 20px;
  text-decoration: none;
  cursor: pointer;
  gap: 15px;
  text-align: center;
  transition: 0.14s ease-out;
`;
export const EmptyCardImage = styled.div`
  max-width: 120px;
  img {
    width: 100%;
    height: auto;
  }
`;
export const EmptyCardContent = styled.div`
  h5 {
    font-weight: 700;
    font-size: 20px;
    line-height: 1.5;
    margin: 0;
    white-space: normal;
    color: rgb(0, 0, 0);
  }

  p {
    color: rgba(60, 60, 67, 0.6);
    font-size: 12px;
    line-height: 1.4;
    margin: 0px;
  }
`;
