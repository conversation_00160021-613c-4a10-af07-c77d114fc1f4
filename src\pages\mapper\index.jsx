import Wrapper from '@/components/layout/wrapper/wrapper';
import MapView from '@/modules/MapView';
import Icon from '@/components/shared/Icon';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  NavigationSearch,
  SearchInput,
  StateCard,
  StateCardBody,
  StateCardHeader,
  Toolbars,
  ToolbarsInner,
  StateCardHead,
  MapAttrbutes,
  MapAttrbuteControl,
  MapAttrbuteLogo,
} from '../../modules/MapView/index.styled';
import { clearDrawnRoute, clearUrlParams, debounce, searchLocations, updateUrl } from '../../shared/utils';
// import { motion, AnimatePresence, steps } from 'framer-motion';
import { useMap } from '../../context/MapContext';
import DirectionsCard from '../../modules/DirectionsCard';
import ListComponent from '../../modules/ListComponent';
import useUrlParams from '../../hooks/UseUrlParams';
import { AnimatePresence, motion } from 'framer-motion';
import ListCategory from '../../modules/ListCategory';
import MapController from '../../modules/MapController';
import { useLocation } from 'react-router-dom';
import useDeState from '../../hooks/debounceState';
import LocationCard from '../../modules/LocationCard';
import { Loader } from '../../components/loader';
import NewsPanel from '../../modules/NewsPanel';
import DepartmentPanel from '../../modules/DepartmentPanel';
import { isMobile } from 'react-device-detect';
import FeedbackPanel from '../../modules/FeedbackPanel';
import ServicePanel from '../../modules/ServicesPanel';
import BottomBarController from '../../modules/BottomBarController';
import SplashScreen from '../../components/SplashLoader';
import { toast } from 'react-toastify';
import TrackLocationAnalytics from '../../components/analytics';
import CategoriesPanel from '../../modules/CategoriesPanel';
import introJs from 'intro.js';
import 'intro.js/introjs.css';
import useElementHeight from '../../shared/getElementHeight';
import { homeSteps } from '../../shared/common';
import { trackEvent } from '../../shared/analyticsService';
import config from '../../config/envConfig';
let locationFromQueryParam = false;

function Mapper() {
  const splashScreen = import.meta.env.VITE_APP_SPLASH_SCREEN === 'true' && !localStorage.getItem('shownedSplash');
  const {
    map: { referance: map },
    // floorData: { floors },
    allLocations,
    focusOnLocation,
    resetToDefaultViewport,
    isStarted,
    showBottomBar,
    // switchFloor,
    selectLocation,
    clearSelection
  } = useMap();
  const toggleSelect = useRef(false);
  const [navigationSearchRef, height] = useElementHeight();
  const [isFocused, setIsFocused] = useDeState(false);
  const [searchedLocations, setSearchedLocations] = useDeState([]);
  const [selectedLocation, setSelectedLocation] = useDeState(null);
  const [selectedCategory, setSelectedCategory] = useDeState(null);
  const [startDirection, setStartDirection] = useDeState(null);
  const [locationCardHeight, setLocationCardHeight] = useDeState(0);
  const [navigationSearchHeight,setNavigationSearchHeight] = useDeState(0);
  // const [activePanel, setActivePanel] = useStateWithLocalStorage('activePanel-careroutes', 'map');
  const [activePanel, setActivePanel] = useState('map');
  const [search, setSearch] = useDeState('');
  const [urlDirection, setUrlDirection] = useDeState(null);
  const [navigationStarted, setNavigationStarted] = useDeState(null);
  const [loading, setLoading] = useDeState(true);
  const searchInputRef = useRef(null);
  const params = useUrlParams();
  const location = useLocation(); // Access the current location object
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [isBottomSheetExpanded, setIsBottomSheetExpanded] = useState(false);
  const touchStartRef = useRef(null);
  const handleTouchStart = (e) => {
    if (screenWidth <= 763) {
      touchStartRef.current = {
        startY: e.touches[0].clientY,
        scrollTop: e.target.closest('.scroller')?.scrollTop || 0,
      };
    }
  };
  const handleTouchMove = (e) => {
    if (screenWidth <= 763 && touchStartRef.current !== null && selectedLocation) {
      const { startY, scrollTop } = touchStartRef.current;
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;
      if (scrollTop === 0 && deltaY > 0) {
        setIsBottomSheetExpanded(false);
      }
      if (deltaY < -50) {
        setIsBottomSheetExpanded(true);
      }
    }
  };
  const handleTouchEnd = useCallback(() => {
    touchStartRef.current = null; // Reset touch start reference
  }, []);
  useEffect(() => {
    if (map) {
      setLoading(false);
    }
  }, [map, setLoading]);
  useEffect(() => {
    setNavigationSearchHeight(height);
  },[height]);
  useEffect(() => {
    const handleOffline = () => {
      // toast.warn('You are offline');
    };
    const handleOnline = () => {
      // toast.success('You are online');
    };
    window.addEventListener('offline', handleOffline);
    window.addEventListener('online', handleOnline);
    return () => {
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('online', handleOnline);
    };
  }, []);
  useEffect(() => {
    if (selectedLocation) {
      updateUrl({ location: selectedLocation.id });
    } else {
      // clearUrlParams();
    }
  }, [selectedLocation]);
  useEffect(() => {}, []);
  useEffect(() => {
    if (params) {
      const origin = params.find((it) => it.type === 'origin').nodeId;
      const destination = params.find((it) => it.type === 'destination').nodeId;
      const waypoints = params.filter((it) => it.type === 'middle').map((it) => it.nodeId);
      if (origin || destination || waypoints.length > 0) {
        toggleSelect.current = true;
        setSearch('');
        setIsFocused(false);
        setUrlDirection(params);
        // if (!(origin && destination)) {
        //   let flag=false;
        //   if (origin) {
        //     const originId = allLocations.find((it) => it.id === origin)?.nodeId;
        //     console.log({originId});
        //     flag=true;
        //     setTimeout(() => {
        //       originId && selectLocation(originId);
        //     }, 200);
        //   }
        //   if (destination&& !flag) {
        //     const destinationId = allLocations.find((it) => it.id === destination)?.nodeId;
        //     console.log({destinationId});
        //     setTimeout(() => {
        //       destinationId && selectLocation(destinationId);
        //     }, 200);
        //   }
        // }
      }
    }
  }, [params, map]);

  useEffect(() => {
    try {
      const queryParams = new URLSearchParams(location.search); // Parse the query string
      const locationParam = queryParams.get('location');
      const find = allLocations.find((it) => it.id === locationParam);
      if (find && !locationFromQueryParam) {
        locationFromQueryParam = true;
        setSelectedLocation(find);
        // const findFloorIndex = floors.findIndex((it) => it.id === find?.floor?.id);
        setTimeout(() => {
          selectLocation(find.nodeId);
        }, 200);
      }
    } catch (error) {
      console.error({ error, message: 'Load location from url' });
    }
  }, [allLocations]);

  useEffect(() => {
    const handleResize = () => setScreenWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (map && allLocations.length) {
      map.eventsHandler.on('select', (location) => {
        if (toggleSelect.current) return;
        if (location?.locations.length === 1) {
          const find = allLocations.find((it) => it.id === location.locations[0].id);
          if (!find) return;
          // setNavigationStarted(null);
          setSelectedLocation(null);
          setTimeout(() => {
            trackEvent('map_location_selected', { location: find?.name, id: find?.id });
            setSelectedLocation(find);
          });
          // setStartDirection(null);
          // setUrlDirection(null);
          focusOnLocation(find.nodeId);
        }
      });
    }
    return () => {
      if (map) {
        map.eventsHandler.off('select');
      }
    };
  }, [map, allLocations, focusOnLocation, setSelectedLocation]);
  useEffect(() => {
    const searchdata = async () => {
      const [locationData] = await Promise.all([
        searchLocations(map, search),
        // searchCategories(map, search),
      ]);
      setSearchedLocations([
        ...locationData
          .map((it) => {
            const { id, name, description, logo, polygonId, ...other } = it;
            return {
              name,
              subtitle: description,
              logo: logo || '',
              nodeId: it,
              polygonId,
              ...other,
              id,
              ItemType: 'location',
            };
          })
          .filter((it) => it.nodeId)
          .filter((it) => it.type !== 'MAP_OBJECT'),
        // ...categoryData.map((it) => {
        //   const { id, name, icon, color, ...other } = it;
        //   return {
        //     name,
        //     color,
        //     logo: icon || '',
        //     ...other,
        //     id,
        //     ItemType: 'category',
        //   };
        // }),
      ]);
    };
    searchdata();
  }, [map, search]);
  // if (!map) return;
  // const slectedLocationHours = useMemo(() => {
  //   if (selectedLocation) {
  //     return processOperationHours(selectedLocation);
  //   } else {
  //     return null;
  //   }
  // }, [selectedLocation]);
  // const isStoreOpen = useMemo(() => {
  //   return slectedLocationHours
  //     ? slectedLocationHours?.todaysData?.opens && slectedLocationHours?.todaysData?.closes
  //     : false;
  // }, [slectedLocationHours]);

  const haveTopLocations = useMemo(() => {
    return allLocations.filter((it) => it.topLocation).length > 0;
  }, [allLocations]);

  const [windowHeight, setWindowHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handler = () => {
      const newHeight = window.innerHeight;
      setWindowHeight(newHeight);
    };
    
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handler);
    }
    
    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handler);
      }
    };
  }, []);



  const dynamicHeight = useMemo(() => {
    const defaultHeight = !haveTopLocations ? (screenWidth <= 763 ? `${navigationSearchHeight + 21}px` : '100px') : '200px';
    if (startDirection) {
      return screenWidth <= 763 ? 'fit-content' : 'auto'; // When there is a search query
    }

    if (selectedLocation) {
      if (screenWidth <= 763) {
        return isBottomSheetExpanded ? '100dvh' : `${locationCardHeight + 33}px`; // Bottom sheet behavior
      }
      return 'auto'; // Default for larger screens
    }

    if (isFocused) {
      return `${windowHeight - 50}px`;
    }
    if (selectedCategory) {
      return 'auto'; // When a category is selected
    }

    if (search) {
      return screenWidth <= 763 ? defaultHeight : '250px'; // When there is a search query
    }

    return defaultHeight; // Default height
  }, [
    startDirection,
    isFocused,
    selectedCategory,
    selectedLocation,
    search,
    screenWidth,
    isBottomSheetExpanded,
    haveTopLocations,
    locationCardHeight,
    navigationSearchHeight,
    windowHeight,
  ]);
  // console.groupCollapsed('Mapper states'); // for debugging
  // console.log('navigationStarted', navigationStarted);
  // console.groupEnd();
  console.log({height})
  useEffect(() => {
    if (activePanel !== 'map') {
      toggleSelect.current = false;
      clearDrawnRoute(map);
      clearUrlParams();
      setSelectedLocation(null);
      setSelectedCategory(null);
      setSearch('');
      setIsFocused(false);
      setStartDirection(null);
      setUrlDirection(null);
      setNavigationStarted(null);
      resetToDefaultViewport();
      // clearSelection();
    }
  }, [activePanel]);
  console.log("-------",{locationCardHeight})
  const onNavigateNewsPanel = useCallback(
    (item) => {
      setActivePanel('map');
      const find = allLocations.find((it) => it.id === item);
      selectLocation(find.nodeId);
      setSelectedLocation(find);
      setIsFocused(false);
      setSearch('');
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [allLocations]
  );

  useEffect(() => {
    if (!config.TUTORIAL || !map || localStorage.getItem('tourCompletedHomepage')) return;
  
    const intro = introJs();
    const steps = homeSteps.map(step => ({
      element: document.querySelector(step.target),
      intro: step.content,
      // position: step.position || 'bottom',
    }));
  
    intro.setOptions({
      steps: steps,
      showBullets: true,
      exitOnOverlayClick: false,
      keyboardNavigation: false,
      doneLabel: 'Finish',
      nextLabel: 'Next',
      prevLabel: 'Previous',
      hidePrev: true
    });
  
   
  
    intro.onexit(() => {
      localStorage.setItem('tourCompletedHomepage', true);
    });
  
    if (!isMobile || !showBottomBar) {
      intro.setOptions({
        steps: steps.slice(0, 1).concat(steps.slice(-1))
      });
    }
  
    intro.start();
  
    return () => {
      intro.exit();
    };
  }, [map, showBottomBar]);
  return (
    <>
    <TrackLocationAnalytics />
      <SplashScreen active={!!map}/>
      <Wrapper>
        {showBottomBar && isMobile && !startDirection && !urlDirection ? (
          <BottomBarController activePanel={activePanel} onSelect={(panel) => setActivePanel(panel)} />
        ) : (
          ''
        )}
        <NewsPanel
          open={activePanel === 'news'}
          onNavigate={onNavigateNewsPanel}
        />
        <DepartmentPanel
          open={activePanel === 'departments'}
          onNavigate={(item) => {
            setActivePanel('map');
            const find = allLocations.find((it) => it.id === item.id);
            selectLocation(find.nodeId);
            setSelectedLocation(find);
            setIsFocused(false);
            setSearch('');
          }}
        />
        <FeedbackPanel open={activePanel === 'feedback'} />
        <ServicePanel
          open={activePanel === 'service'}
          onNavigate={(item) => {
            setActivePanel('map');
            const find = allLocations.find((it) => it.id === item.id);
            selectLocation(find.nodeId);
            setSelectedLocation(find);
            setIsFocused(false);
            setSearch('');
          }}
        />
        <CategoriesPanel
          open={activePanel === 'categories'}
          onNavigate={(item) => {
            setActivePanel('map');
            setSelectedLocation(null);
            setSelectedCategory({ ...item.category });
            setIsFocused(true);
            setTimeout(() => {
              searchInputRef.current.focus();
            }, 50);
          }}
        />

        <Toolbars
          style={{
            paddingBottom: isMobile && showBottomBar ? (isStarted ? '0px' : '54px') : '0px',
          }}
        >
          <ToolbarsInner
            startNavigation={isStarted}
            isDirection={
              !navigationStarted && ((selectedLocation && !startDirection) || (!selectedLocation && !urlDirection))
            }
          >
            {/* <NavigateOnMap /> */}
            {!navigationStarted && ((selectedLocation && !startDirection) || (!selectedLocation && !urlDirection)) && (
              <motion.div
                style={{
                  maxHeight: isMobile && showBottomBar ? 'calc(100dvh - 74px)' : 'calc(100dvh - 20px)',
                  position: ' relative',
                  width: '100%',
                  willChange: 'transform',
                  zIndex: 51,
                }}
                initial={{ height: dynamicHeight }}
                animate={{ height: dynamicHeight }}
                transition={{ type: 'spring', stiffness: 100, damping: 20, duration: 0.3 }}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                <div
                  style={{
                    position: ' relative',
                    height: '100%',
                  }}
                >
                  <StateCard>
                    {!selectedLocation && !urlDirection && (
                      <>
                        <StateCardHeader>
                          <NavigationSearch 
                              className="step1"
                              ref={navigationSearchRef}>
                            <AnimatePresence mode="wait">
                              {isFocused || selectedCategory ? (
                                <motion.div
                                  key="arrow-icon"
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: -20 }}
                                  transition={{ duration: 0.3 }}
                                  style={{ display: 'inline-block' }}
                                >
                                  <Icon
                                    onClick={() => {
                                      setIsFocused(false);
                                      setSelectedCategory(null);
                                      setSearch('');
                                    }}
                                    icon="arrow-narrow-left"
                                    size="22"
                                  />
                                </motion.div>
                              ) : (
                                <motion.div
                                  key="search-icon"
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  exit={{ opacity: 0, x: -20 }}
                                  transition={{ duration: 0.3 }}
                                  style={{ display: 'inline-block' }}
                                >
                                  <Icon
                                    onClick={() => {
                                      setIsFocused(false);
                                      setSelectedCategory(null);
                                    }}
                                    icon="search-md"
                                    size="22"
                                  />
                                </motion.div>
                              )}
                            </AnimatePresence>
                            <SearchInput
                              ref={searchInputRef}
                              {...(selectedCategory
                                ? {
                                    readOnly: true,
                                    value: selectedCategory.name,
                                  }
                                : { value: search })}
                              placeholder="I want to go to"
                              onFocus={() => {
                                setIsFocused(true);
                                if (selectedCategory) {
                                  setSearch(selectedCategory.name);
                                  setSelectedCategory(null);
                                }
                              }}
                              onChange={({ target: { value } }) => setSearch(value)}
                            />
                          </NavigationSearch>
                        </StateCardHeader>
                        {/* <StateCardBody>
                      {allAmenities.map((it, index) => (
                        <span
                          onClick={() => {
                            showAmenity(it);
                          }}
                          key={index}
                        >
                          {it}
                        </span>
                      ))}
                    </StateCardBody> */}
                        {/* <StateCardBody>
                    <StateCardHead>
                      <h5>Find Nearby</h5>
                    </StateCardHead>
                    <CategoriesListWrapper>
                      {allCategories.map((it, index) => (
                        <CategoriesListItem key={index}>
                          <img src="https://beco-lightning-backend.s3.ap-southeast-1.amazonaws.com/categories/feeding-room.svg" />
                          <h5>Restaurants</h5>
                        </CategoriesListItem>
                      ))}
                    </CategoriesListWrapper>
                  </StateCardBody> */}
                        {!isFocused && !search && !selectedCategory ? (
                          <>
                            {allLocations.filter((it) => it.topLocation).length ? (
                              <StateCardBody className="scroller">
                                <StateCardHead>
                                  <h5>Most Popular</h5>
                                </StateCardHead>
                                <ListCategory
                                  listData={allLocations.filter((it) => it.topLocation)}
                                  limiter={4}
                                  onSelect={(item) => {
                                    const find = allLocations.find((it) => it.id === item.id);
                                    selectLocation(find.nodeId);
                                    setSelectedLocation(find);
                                    setIsFocused(false);
                                    setSearch('');
                                  }}
                                />
                              </StateCardBody>
                            ) : (
                              ''
                            )}
                          </>
                        ) : (
                          <StateCardBody className="scroller">
                            <StateCardHead>
                              <h5>
                                {search || selectedCategory
                                  ? 'Locations'
                                  : haveTopLocations
                                    ? 'Most Popular'
                                    : 'Locations'}
                              </h5>
                            </StateCardHead>
                            {(() => {
                              return '';
                            })()}
                            <ListComponent
                              listData={
                                search
                                  ? searchedLocations
                                  : selectedCategory
                                    ? allLocations.filter((it) =>
                                        (it.categories || []).map((it) => it.id).includes(selectedCategory.id)
                                      )
                                    : allLocations.filter((it) => (haveTopLocations ? it.topLocation : true))
                              }
                              onSelect={(item) => {
                                const find = allLocations.find((it) => it.id === item.id);
                                selectLocation(find.nodeId);
                                setSelectedLocation(find);
                                setIsFocused(false);
                                setSearch('');
                              }}
                              emptyMessage={
                                search || selectedCategory
                                  ? 'No locations were found'
                                  : 'No popular locations were found'
                              }
                            />
                          </StateCardBody>
                        )}
                      </>
                    )}

                    {selectedLocation && !startDirection && (
                      <LocationCard
                        overFlow={screenWidth <= 763 && !isBottomSheetExpanded ? true : false}
                        location={selectedLocation}
                        onSelectCategory={(category) => {
                          setSelectedLocation(null);
                          setSelectedCategory({ ...category });
                          setIsFocused(true);
                          setTimeout(() => {
                            searchInputRef.current.focus();
                          }, 50);
                          trackEvent('location_category_selected', { location: selectedLocation?.name, id: selectedLocation?.id, category: category?.name, categoryId: category?.id });
                        }}
                        cancelCard={() => {
                          clearUrlParams();
                          setSearch('');
                          setSelectedLocation(null);
                          resetToDefaultViewport();
                          setIsBottomSheetExpanded(false);
                          try {
                            map.clearSelection();
                          } catch (error) {
                            console.log('Clear Selection', { Error: error });
                          }
                        }}
                        startDirection={() => {
                          clearUrlParams();
                          try {
                            setSearch('');
                            setIsFocused(false);
                            setStartDirection({
                              arrival: selectedLocation,
                              departure: null,
                              waypoints: [],
                              steps: [],
                            });
                            toggleSelect.current = true;
                            setTimeout(() => {
                              map.clearSelection();
                            }, 1000);
                            trackEvent('direction_started', { location: selectedLocation?.name, id: selectedLocation?.id });
                          } catch (e) {
                            console.log('Clear Selection', { Error: e });
                          }
                        }}
                        getParentHeight={(height)=>{
                          setLocationCardHeight(height)
                        }}
                      />
                    )}
                  </StateCard>
                </div>
              </motion.div>
            )}

            {startDirection || urlDirection ? (
              <DirectionsCard
                setNavigationStarted={setNavigationStarted}
                urlDirection={urlDirection}
                data={startDirection}
                screenWidth={screenWidth}
                cancel={() => {
                  setStartDirection(null);
                  setUrlDirection(null);
                  toggleSelect.current = false;
                }}
              />
            ) : (
              ''
            )}
          </ToolbarsInner>
        </Toolbars>
        <MapController />
        <MapView />
        {!navigationStarted && ((selectedLocation && !startDirection) || (!selectedLocation && !urlDirection)) &&  (
          <MapAttrbutes
          // top={dynamicHeight}
          // initial={{ bottom: dynamicHeight }}
          // animate={{ bottom: dynamicHeight }}
          // transition={{ type: 'spring', stiffness: 100, damping: 20, duration: 0.3 }}
        >
          <MapAttrbuteControl>
            <MapAttrbuteLogo style={{ backgroundImage: 'url(/logo-full-attr.png)' }} />
          </MapAttrbuteControl>
        </MapAttrbutes>)}
      </Wrapper>
    </>
  );
}

export default Mapper;
