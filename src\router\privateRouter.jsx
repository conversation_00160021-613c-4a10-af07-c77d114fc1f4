// PrivateRoute.js
import { Navigate, Outlet, useSearchParams } from 'react-router-dom';

const PrivateRoute = () => {
  const [searchParams] = useSearchParams();
  const orgParam = searchParams.get('org');

  // If 'org' parameter is missing, redirect to error page
  if (!orgParam) {
    return <Navigate to="/error" replace />;
  }

  // If 'org' parameter is present, render the child components
  return <Outlet />;
};

export default PrivateRoute;
