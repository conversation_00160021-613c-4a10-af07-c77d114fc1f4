import { useEffect, useMemo, useState } from 'react';
import moment from "moment-timezone";
import {
  ListItemImage,
  LocationCoverImage,
  LocationCoverPhoto,
  LocationDetailsCard,
  ButtonGroup,
  LocationDetailsCardHeader,
  LocationDetailsCardSegment,
  LocationDetailSection,
  LocationDetailsTitle,
  LocationDetailsTitleUnit,
  LocationSubHead,
  StateCardBody,
  TopActionBtn,
  LocationEvent,
  LocationEventHead,
  EventMonth,
  EventDate,
  EventDateGroup,
  LocationEventBody,
  Flex,
  TimelineItem,
  TimelineContainer,
  TimelineContent,
  TimelineTime,
  ItemCard,
  ItemCardIns,
  ListItem,
  ListItemSegment,
  ListItemTitleUnit,
  LocationCategoryList,
  LocationCategory,
  LocationDetailSectionStrip,
  LocationDetailSectionTitle,
  LocationFocusWrapper,
  LocationCollapseSection,
  LocationCollapseSectionIns,
  LocationCollapseSectionLink,
  DepartmentDetails,
  LocationDescription,
} from '../MapView/index.styled';
import RenderOperationHours from '../OperationalHours';
import Button from '@/components/shared/button/button';
import Icon from '@/components/shared/Icon';
import { copyURLToClipboard, formatNumber, processOperationHours } from '../../shared/utils';
import Share from '../../components/share';
import { useMap } from '../../context/MapContext';
import useElementHeight from '../../shared/getElementHeight';
import ExpandableText from '../../components/ExpandableText';

const eventsDates = [
  { date: '2025-03-15', data: 'No event', disabled: true },
  { date: '2025-03-16', data: 'No event', disabled: true },
  { date: '2025-03-17', data: 'Event on March 17', disabled: true },
  { date: '2025-03-18', data: 'Event on March 18', disabled: false },
  { date: '2025-03-19', data: 'Event on March 19', disabled: false },
  { date: '2025-03-20', data: 'Event on March 20', disabled: true },
  { date: '2025-03-21', data: 'No event', disabled: true },
  { date: '2025-03-22', data: 'No event', disabled: true },
];

const LocationCard = ({ overFlow, location, cancelCard, startDirection, page = '', extras = {}, onSelectCategory,getParentHeight }) => {
  const [locationHeaderRef, height] = useElementHeight();  
  const iconBaseUrl = import.meta.env.VITE_APP_BASE_URL_ICON;
  const { happenings } = useMap();
  const { EVENT } = happenings;
  const [filteredNews, setFilteredNews] = useState(EVENT || []);
  const [activeDateId, setActiveDateId] = useState(eventsDates[3].date);

  useEffect(() => {
    setFilteredNews(EVENT || []);
  }, [EVENT]);

  useEffect(() => {
    eventsDates
      .filter((item) => !item.disabled)
      .forEach((eventDate) => {
        if (moment.utc(eventDate.date,'YYYY-MM-DD').tz("Asia/Kolkata").isSame(moment.utc().tz("Asia/Kolkata"), 'day')) {
          setActiveDateId(moment.utc().tz("Asia/Kolkata").format('YYYY-MM-DD'));
          return true;
        }
      });
  }, []);

  useEffect(() => {
    getParentHeight?.(height);
    return ()=>{
      getParentHeight?.(0)
    }
  },[height,getParentHeight])

  const idFilteredData = useMemo(() => {
    return filteredNews?.filter((item) => {
      if (location?.id === item?.locationId) {
        return true;
      } else {
        return false;
      }
    });
  }, [filteredNews, location]);

  const dateFilteredData = useMemo(() => {
    return idFilteredData?.filter((item) => {
      if (moment.utc(item.startDate,'YYYY-MM-DD').tz("Asia/Kolkata").isSame(moment.utc(activeDateId,'YYYY-MM-DD').tz("Asia/Kolkata"), 'day')) {
        return true;
      } else {
        return false;
      }
    });
  }, [idFilteredData, activeDateId]);
  // console.log(dateFilteredData);

  const onClickDate = (item) => {
    setActiveDateId(item.date);
  };
  const geTPersons = (data) => {
    return data ? JSON.parse(data) : [];
  };

  const getTimeDuration = (startDate, endDate) => {
    if (!startDate && !endDate) {
      return;
    }
    const startMoment = moment.utc(startDate).tz("Asia/Kolkata");
    const endMoment = moment.utc(endDate).tz("Asia/Kolkata");

    // Format start and end times in 12-hour format with AM/PM
    const startTime = startMoment.format('h:mm A');
    const endTime = endMoment.format('h:mm A');

    // Return the formatted duration string
    return `${startTime} to ${endTime}`;
  };
  const selectedLocation = location;
  const slectedLocationHours = useMemo(() => {
    if (selectedLocation) {
      return processOperationHours(selectedLocation);
    } else {
      return null;
    }
  }, [selectedLocation]);
  const isStoreOpen = useMemo(() => {
    return slectedLocationHours
      ? slectedLocationHours?.todaysData?.opens && slectedLocationHours?.todaysData?.closes
      : false;
  }, [slectedLocationHours]);

  const buttonLabel = useMemo(() => {
    if (page === 'direction-card') {
      if (extras?.lastIncomplete) {
        switch (extras?.lastIncomplete?.type) {
          case 'origin':
            return 'Set as Starting Point';
          case 'destination':
            return 'Set as Destination';
          default:
            return 'Add Stop';
        }
      }
      return 'Add Stop';
    } else {
      return 'Directions';
    }
  }, [extras, page]);
  const isAmenity = location?.type === 'AMENITIES';
  const bgColor = useMemo(() => {
    return location?.categories?.[0]?.color?.hex || '#41648B';
  }, [location]);

  const imageUrl = useMemo(()=>{
    if(!isAmenity){
      return location?.logo||location?.categories?.[0]?.icon;
    }else{
      return location?.categories?.[0]?.icon||`${iconBaseUrl}${location?.amenity.toLowerCase().replace('_', '-')}.svg`;
    }
  },[isAmenity,location,iconBaseUrl])
  const usingCategoryIcon = useMemo(
    () => !location?.logo && location?.categories?.[0]?.icon,
    [location]
  );
  const phoneNumber = useMemo(() => {
    return selectedLocation?.phone?.phone;
  }, [selectedLocation]);

  // usingCategoryIcon try using this
  if (!location) return null;
  const { links } = location;
  const website = links?.find((link) => link.label.toLowerCase() === 'website')?.url;
  return (
    <StateCardBody card="location" className="scroller" style={{ overflowY: overFlow && 'hidden', marginTop: overFlow && '-10px' }}>
      <div className="bottom-sheet-header">
        <div className="handle"></div>
      </div>
      <LocationDetailsCard>
      <Flex dir="column" gap={overFlow ? "6px" : "10px"} ref={locationHeaderRef}>
        <LocationDetailsCardHeader
          style={{
            position: window.innerWidth >= 735 ? (selectedLocation.banner ? 'absolute' : 'unset') : 'absolute',
          }}
        >
          <TopActionBtn
            onClick={() => {
              cancelCard && cancelCard();
            }}
          >
            <Icon
              icon={window.innerWidth >= 734 ? 'arrow-left' : 'x'}
              size={'20'}
              color={selectedLocation.banner ? '#fff' : ' #000'}
            />
          </TopActionBtn>
          <Share
            callback={() => {
              // Get the current URL
              const currentUrl = new URL(window.location.href);
              // Preserve the "org" parameter if it exists
              const orgParam = currentUrl.searchParams.get('org');
              // Clear all existing query parameters
              currentUrl.search = '';
              // Re-add the "org" parameter (if it exists)
              if (orgParam) {
                currentUrl.searchParams.set('org', orgParam);
              }
              currentUrl.searchParams.set('location', selectedLocation?.id);
              copyURLToClipboard(currentUrl.toString());
            }}
            color={selectedLocation.banner ? '#fff' : ' #000'}
          />
        </LocationDetailsCardHeader>
        {window.innerWidth > 734 && selectedLocation.banner && (
          <LocationCoverPhoto>
            <LocationCoverImage
              style={{
                backgroundImage: `url(${selectedLocation.banner || ''})`,
              }}
            ></LocationCoverImage>
          </LocationCoverPhoto>
        )}
          {!overFlow && (
        <LocationDetailsCardSegment>
            <ListItemImage
              style={{
                '--size': window.innerWidth <= 734 ? '50px' : '60px',
                top: selectedLocation.banner ? '-40px' : '0px',
                left: selectedLocation.banner ? '15px' : '0px',
                marginBottom: selectedLocation.banner ? '-40px' : '0px',
                backgroundColor: bgColor,
              }}
            >
              {imageUrl ? (
                <img src={imageUrl} alt="Location logo" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M19.717 20.362C21.143 19.585 22 18.587 22 17.5c0-1.152-.963-2.204-2.546-3C17.623 13.58 14.962 13 12 13s-5.623.58-7.454 1.5C2.963 15.296 2 16.348 2 17.5s.963 2.204 2.546 3C6.377 21.42 9.038 22 12 22c3.107 0 5.882-.637 7.717-1.638"
                    opacity="0.5"
                  />
                  <path
                    fill="currentColor"
                    fillRule="evenodd"
                    d="M5 8.515C5 4.917 8.134 2 12 2s7 2.917 7 6.515c0 3.57-2.234 7.735-5.72 9.225a3.28 3.28 0 0 1-2.56 0C7.234 16.25 5 12.084 5 8.515M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {/* {isAmenity ? (
                <img
                  style={{ filter: 'invert(1)' }}
                  src={`${iconBaseUrl}${selectedLocation?.amenity.toLowerCase()}.svg`}
                  alt="Location logo"
                />
              ) : selectedLocation?.logo ? (
                <img src={selectedLocation?.logo} alt="Location logo" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M19.717 20.362C21.143 19.585 22 18.587 22 17.5c0-1.152-.963-2.204-2.546-3C17.623 13.58 14.962 13 12 13s-5.623.58-7.454 1.5C2.963 15.296 2 16.348 2 17.5s.963 2.204 2.546 3C6.377 21.42 9.038 22 12 22c3.107 0 5.882-.637 7.717-1.638"
                    opacity="0.5"
                  />
                  <path
                    fill="currentColor"
                    fillRule="evenodd"
                    d="M5 8.515C5 4.917 8.134 2 12 2s7 2.917 7 6.515c0 3.57-2.234 7.735-5.72 9.225a3.28 3.28 0 0 1-2.56 0C7.234 16.25 5 12.084 5 8.515M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4"
                    clipRule="evenodd"
                  />
                </svg>
              )} */}
            </ListItemImage>
        </LocationDetailsCardSegment>
          )}
        <LocationDetailsCardSegment>
          <LocationDetailsTitle className={overFlow? 'sm': ''}>
            <h1>{selectedLocation?.name}</h1>
            <LocationDetailsTitleUnit>
              <p>{selectedLocation?.floor?.shortName}</p>
              {/* {!isAmenity && <p className={isStoreOpen ? 'opened' : 'closed'}>{isStoreOpen ? 'Open' : 'Closed'}</p>} */}
            </LocationDetailsTitleUnit>
          </LocationDetailsTitle>
        </LocationDetailsCardSegment>
        <ButtonGroup>
          <Button
            onClick={() => {
              startDirection && startDirection();
            }}
            type="button"
            variant="primary"
            style={{ flex: 1 }}
          >
            {buttonLabel}
            {/* Add Stop */}
            {/* Set as Starting Point */}
            {/* Set as Destination */}
          </Button>
          {/* {phoneNumber && (
            <Button
              onClick={() => {
                window.open(`tel:${phoneNumber}`);
              }}
              variant="grey"
              icon={<Icon icon="phone-call-01" />}
            />
          )} */}

          {website && (
            <Button
              onClick={() => {
                window.open(website, '_blank');
              }}
              variant="grey"
              icon={<Icon icon="globe-01" />}
            />
          )}
        </ButtonGroup>
      </Flex>
        {idFilteredData.length > 0 ? (
          <LocationDetailsCardSegment direction="column">
            <LocationSubHead>
              <h2>Events</h2>
            </LocationSubHead>

            <LocationDetailSection
              style={{
                padding: 0,
              }}
            >
              <LocationEvent>
                <LocationEventHead>
                  <Flex align="center" justify="center" gap={'12px'}>
                    {eventsDates.map((eventdate) => {
                      return (
                        <EventDateGroup
                          key={eventdate.date}
                          active={moment.utc(eventdate.date,'YYYY-MM-DD').tz("Asia/Kolkata").isSame(moment.utc(activeDateId,'YYYY-MM-DD').tz("Asia/Kolkata"), 'day') ? true : false}
                          onClick={() => onClickDate(eventdate)}
                          disabled={eventdate.disabled}
                        >
                          <EventMonth>{moment.utc(eventdate.date).tz("Asia/Kolkata").format('MMM')}</EventMonth>
                          <EventDate>{moment.utc(eventdate.date).tz("Asia/Kolkata").format('DD')}</EventDate>
                        </EventDateGroup>
                      );
                    })}
                  </Flex>
                </LocationEventHead>
                <LocationEventBody>
                  {dateFilteredData?.length > 0 ? (
                    <>
                      {dateFilteredData.sort((a, b) => moment.utc(a.startDate).tz("Asia/Kolkata").diff(moment.utc(b.startDate).tz("Asia/Kolkata"))).map(({ customFields, startDate, endDate, name, description }, index) => {
                        return (
                          <TimelineContainer key={index} noLine>
                            <TimelineItem>
                              <TimelineTime>
                                <span>{getTimeDuration(startDate, endDate)}</span>
                              </TimelineTime>
                              <TimelineContent>
                                <ItemCard style={{ padding: '5px', backgroundColor: 'rgba(0, 0, 0, 0.06)' }}>
                                  <ItemCardIns>
                                    <Flex align="center" gap={'12px'} style={{ padding: '10px' }}>
                                      <ListItem>
                                        <ListItemSegment>
                                          <ListItemTitleUnit>
                                            <p
                                              className="list-title"
                                              style={{
                                                fontSize: '16px',
                                                marginBottom: '0',
                                              }}
                                            >
                                              {name}
                                            </p>
                                          </ListItemTitleUnit>
                                        </ListItemSegment>
                                        {description.trim() !== '' && (
                                          <DepartmentDetails>
                                            <ExpandableText previewLimit={70}>{description || ''}</ExpandableText>
                                          </DepartmentDetails>
                                        )}
                                        {geTPersons(customFields?.Speaker?.data).map((speaker, index) => {
                                          return (
                                            <Flex
                                              key={`s${index}`}
                                              align="center"
                                              style={{
                                                marginBottom: '0',
                                                border: '1px solid rgb(230, 230, 230)',
                                                padding: '8px 7px',
                                                borderRadius: '16px',
                                                backgroundColor: '#fff',
                                              }}
                                            >
                                              <div
                                                style={{
                                                  marginRight: '4px',
                                                  padding: '0',
                                                  flex: '0 0 auto',
                                                  width: 'auto',
                                                  color: '#34a853',
                                                  marginTop: '-2px',
                                                }}
                                              >
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  width="23"
                                                  height="23"
                                                  viewBox="0 0 24 24"
                                                >
                                                  <path
                                                    fill="currentColor"
                                                    fillRule="evenodd"
                                                    d="M4 9a.75.75 0 0 1 .75.75v1a7.25 7.25 0 1 0 14.5 0v-1a.75.75 0 0 1 1.5 0v1a8.75 8.75 0 0 1-8 8.718v2.282a.75.75 0 0 1-1.5 0v-2.282a8.75 8.75 0 0 1-8-8.718v-1A.75.75 0 0 1 4 9"
                                                    clipRule="evenodd"
                                                  />
                                                  <path
                                                    fill="currentColor"
                                                    fillRule="evenodd"
                                                    d="M12 2a5.75 5.75 0 0 0-5.75 5.75v3a5.75 5.75 0 0 0 11.5 0v-3A5.75 5.75 0 0 0 12 2m2 9.5a.75.75 0 0 0 0-1.5h-4a.75.75 0 0 0 0 1.5zm-.25-3.75a.75.75 0 0 1-.75.75h-2A.75.75 0 0 1 11 7h2a.75.75 0 0 1 .75.75"
                                                    clipRule="evenodd"
                                                    opacity="0.5"
                                                  />
                                                  <path
                                                    fill="currentColor"
                                                    d="M14 11.5a.75.75 0 0 0 0-1.5h-4a.75.75 0 0 0 0 1.5zm-1-3A.75.75 0 0 0 13 7h-2a.75.75 0 0 0 0 1.5z"
                                                  />
                                                </svg>
                                              </div>
                                              <div
                                                style={{
                                                  fontSize: '13px',
                                                  textAlign: 'left',
                                                  flex: '1 0 0%',
                                                }}
                                              >
                                                <Flex dir="column" gap="0px">
                                                  <p
                                                    style={{
                                                      padding: '0',
                                                      fontWeight: '600',
                                                      width: 'auto',
                                                      margin: '0',
                                                      fontSize: '14px',
                                                      lineHeight: '1.2',
                                                      color: 'rgba(0,0,0,1)',
                                                    }}
                                                  >
                                                    {speaker?.name}
                                                  </p>
                                                  <span
                                                    style={{
                                                      marginRight: '4px',
                                                      padding: '0',
                                                      flex: '0 0 auto',
                                                      width: 'auto',
                                                      color: 'rgba(0,0,0,.5)',
                                                    }}
                                                  >
                                                    {speaker?.role}
                                                  </span>
                                                </Flex>
                                              </div>
                                            </Flex>
                                          );
                                        })}
                                      </ListItem>
                                    </Flex>
                                  </ItemCardIns>
                                </ItemCard>
                                {/* <h4 className="title">Welcome Address </h4>
                      <p className="description">
                        <b>Dr Preetha Reddy</b>, Executive Vice Chairperson, Apollo Hospitals Group
                      </p> */}
                              </TimelineContent>
                            </TimelineItem>
                          </TimelineContainer>
                        );
                      })}
                    </>
                  ) : (
                    <div>No events found</div>
                  )}
                </LocationEventBody>
              </LocationEvent>
            </LocationDetailSection>
          </LocationDetailsCardSegment>
        ) : (
          <>
            {window.innerWidth <= 734 && selectedLocation.banner && (
              <LocationCoverPhoto
                style={{
                  marginTop: '8px',
                }}
              >
                <LocationCoverImage
                  style={{
                    backgroundImage: `url(${selectedLocation.banner || ''})`,
                  }}
                ></LocationCoverImage>
              </LocationCoverPhoto>
            )}

            {!isAmenity && (
              <>
                <LocationDetailsCardSegment direction="column">
                  <LocationSubHead>
                    <h2>Categories</h2>
                  </LocationSubHead>

                  <LocationDetailSection>
                    <LocationCategoryList>
                      {(selectedLocation?.categories || []).map((it, index) => (
                        <LocationCategory
                          onClick={() => {
                            onSelectCategory && onSelectCategory(it);
                            // selectLocationsByCategory(it);
                          }}
                          key={'links' + index}
                        >
                          {it.name}
                        </LocationCategory>
                      ))}
                    </LocationCategoryList>
                  </LocationDetailSection>
                </LocationDetailsCardSegment>
                {(website || selectedLocation?.description?.trim()) ? <LocationDetailsCardSegment direction="column">
                  <LocationSubHead>
                    <h2>Details</h2>
                  </LocationSubHead>
                  {/* <RenderOperationHours
                    location={selectedLocation}
                    hoursData={selectedLocation?.operationHours || []}
                  /> */}
                  {selectedLocation?.description?.trim() && (<LocationDetailSection>
                    <LocationDescription>
                      <ExpandableText previewLimit={70}>{selectedLocation?.description || ''}</ExpandableText>
                    </LocationDescription>
                  </LocationDetailSection>)}
                  {website&&<LocationDetailSection strips>
                      <LocationDetailSectionStrip>
                        <LocationDetailSectionTitle>Website</LocationDetailSectionTitle>
                        <LocationFocusWrapper>
                          <LocationCollapseSection>
                            <LocationCollapseSectionIns>
                              <LocationCollapseSectionLink href={website} target="_blank">
                                {website}
                              </LocationCollapseSectionLink>
                            </LocationCollapseSectionIns>
                          </LocationCollapseSection>
                        </LocationFocusWrapper>
                      </LocationDetailSectionStrip>
                    
                    {/* {phoneNumber && (
                      <LocationDetailSectionStrip>
                        <LocationDetailSectionTitle>Phone Number</LocationDetailSectionTitle>
                        <LocationFocusWrapper>
                          <LocationCollapseSection>
                            <LocationCollapseSectionIns>
                              <LocationCollapseSectionLink
                                href={`tel:${phoneNumber}`}
                                target="_blank"
                              >
                                {formatNumber(phoneNumber)}
                              </LocationCollapseSectionLink>
                            </LocationCollapseSectionIns>
                          </LocationCollapseSection>
                        </LocationFocusWrapper>
                      </LocationDetailSectionStrip>
                    )} */}
                  </LocationDetailSection>}
                </LocationDetailsCardSegment>:""}
              </>
            )}
          </>
        )}
      </LocationDetailsCard>
    </StateCardBody>
  );
};

export default LocationCard;
