import React, { CSSProperties, SVGProps } from 'react';
import iconSet from '@/assets/icon/selection.json';

interface IconSet {
    IcoMoonType: string;
    icons: {
        icon: {
            paths: string[];
            attrs: {}[];
            isMulticolor: boolean;
            isMulticolor2: boolean;
            grid: number;
            tags: string[];
            width?: number;
        };
        attrs: {}[];
        properties: {
            order: number;
            id: number;
            name: string;
            prevSize: number;
            code: number;
        };
        setIdx: number;
        setId: number;
        iconIdx: number;
    }[];
    height: number;
}

interface IconProps extends SVGProps<SVGSVGElement> {
    icon: string;
    color?: string;
    size?: string | number;
    className?: string;
    style?: CSSProperties;
}

const icons = iconSet as unknown as IconSet;

function getIcon(
    icon: string,
    styles: { svg: CSSProperties; path: CSSProperties },
    size: string | number,
    className: string,
    rest: SVGProps<SVGSVGElement>
) {
    const find = (ic: { properties: { name: string } }) =>
        ic.properties.name.split(', ').includes(icon);
    const currentIcon = icons.icons.find(find);

    const getPath = (item: { attrs?: Array<SVGProps<SVGPathElement>> }) => (
        path: string,
        index: number
    ) => {
        const attrs = (item.attrs && item.attrs[index]) || {};
        return <path style={styles.path} key={index} d={path} {...attrs} />;
    };

    if (currentIcon) {
        return (
            <svg
                className={className}
                style={styles.svg}
                width={size}
                height={size}
                viewBox={`0 0 ${currentIcon.icon.width || '1024'} 1024`}
                xmlns="http://www.w3.org/2000/svg"
                {...rest}
                data-testid="icon"
            >
                {currentIcon.icon.paths.map(getPath(currentIcon.icon))}
            </svg>
        );
    }
    return null;
}

const Icon: React.FC<IconProps> = (props) => {
    const {
        color,
        size = '24px',
        icon,
        className = '',
        style = {},
        ...rest
    } = props;

    const styles = {
        svg: {
            display: 'inline-block',
            verticalAlign: 'middle',
            ...style,
        },
        path: {
            fill: color || 'currentColor',
        },
    };

    return getIcon(icon, styles, size, className, rest);
};

export default Icon;
