import { motion } from 'framer-motion';
import styled from 'styled-components';
import WarningIcon from './icons/WarningIcon';
import { useLocation } from 'react-router-dom';

const Container = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100dvh;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
`;

const IconWrapper = styled(motion.div)`
  color: #e11d48;
  margin-bottom: 2rem;
`;

const Title = styled(motion.h1)`
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  text-align: center;
`;

const Message = styled(motion.p)`
  font-size: clamp(1rem, 3vw, 1.25rem);
  color: #4b5563;
  margin-bottom: 2rem;
  text-align: center;
  max-width: 600px;
  line-height: 1.6;
`;

const Button = styled(motion.button)`
  background-color: #2563eb;
  color: white;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);

  @media (max-width: 640px) {
    padding: 0.6rem 1.5rem;
    font-size: 0.875rem;
  }
`;

const ErrorCode = styled(motion.div)`
  font-family: monospace;
  font-size: 1.125rem;
  color: #6b7280;
  margin-top: 2rem;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
`;

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
  },
};

const ErrorPage = ({ onRetry = () => window.location.reload() }) => {
  const location = useLocation();
  let errorMessage = "We couldn't find the page you're looking for.";
  const { message } = location?.state || {};
  return (
    <Container variants={containerVariants} initial="hidden" animate="visible">
      <IconWrapper variants={itemVariants}>
        <WarningIcon />
      </IconWrapper>

      <Title variants={itemVariants}>Oops! Something went wrong</Title>

      <Message variants={itemVariants}>{message || errorMessage}</Message>

      {/* <Button
        variants={itemVariants}
        onClick={onRetry}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        Try Again
      </Button> */}

      {/* <ErrorCode
        variants={itemVariants}
        whileHover={{
          scale: 1.05,
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
        }}
      >
        Error {code}
      </ErrorCode> */}
    </Container>
  );
};

export default ErrorPage;
