import styled from 'styled-components';

export const CardOuter = styled.div`
  touch-action: none;
  color: rgb(0, 0, 0);
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  z-index: 3;
  border-radius: 1.5rem;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(83, 83, 83, 0.15) 0px 4px 100px 10px;
  padding: 20px;
  height: 100%;
  width: 360px;
  max-height: 308px;
  pointer-events: all;
  position: relative;
  transform: translateZ(0px);
`;

export const CardHeader = styled.div`
  display: flex;
  flex-direction: column;
`;

export const CardBody = styled.div`
  display: flex;
  flex-direction: column;
`;
