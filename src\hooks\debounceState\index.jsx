import { useState, useMemo } from 'react';

function debounce(func, delay) {
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func(...args);
    }, delay);
  };
}

function useDeState(initialValue, delay) {
  const [state, setState] = useState(initialValue);

  // Create a debounced version of the state setter
  const debouncedSetState = useMemo(
    () =>
      debounce((value) => {
        setState(value);
      }, delay),
    [delay]
  );

  return [state, debouncedSetState];
}
export default useDeState;
