import React, { useEffect, useState } from 'react';
// import maplibregl from 'maplibre-gl';
// import 'maplibre-gl/dist/maplibre-gl.css';
import { MapContainer } from './index.styled';
import { useMap } from '../../context/MapContext';

const MapView = () => {
  const { mapContainer } = useMap();
  return <MapContainer ref={mapContainer}>{/* <BecoMapView></BecoMapView> */}</MapContainer>;
};

export default React.memo(MapView);
