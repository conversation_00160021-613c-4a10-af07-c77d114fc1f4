import { useMap } from '../../context/MapContext';
import { trackEvent } from '../../shared/analyticsService';
import { categorySortOrder } from '../../shared/common';
import {
  CategoriesListItem,
  CategoriesListWrapper,
  ListItemImage,
  Panel,
  PanelBody,
  PanelHeader,
  PanelInner,
  PanelTitle,
} from '../MapView/index.styled';
import { motion } from 'framer-motion';

const CategoriesPanel = ({ open = false, onNavigate }) => {
  const { allCategories } = useMap();

  return (
    <Panel id="service-panel" open={open}>
      <PanelInner>
        <PanelHeader id="panel-header">
          <PanelTitle>Categories</PanelTitle>
        </PanelHeader>
        <PanelBody>
          <CategoriesListWrapper>
            {(allCategories||[]).sort((a, b) => {
                let indexA = categorySortOrder.indexOf(a.label);
                let indexB = categorySortOrder.indexOf(b.label);

                // If not found in categorySortOrder list, place it at the end
                if (indexA === -1) indexA = categorySortOrder.length;
                if (indexB === -1) indexB = categorySortOrder.length;

                return indexA - indexB;
              }).map((item) => (
              <CategoriesListItem key={item.label} onClick={() => {
                onNavigate(item);
                trackEvent('category_selected', { category: item.label });
              }}>
                <ListItemImage
                  style={{ filter: 'none', backgroundColor: item.color?.hex || '', border: 'none', padding: '10px' }}
                >
                  <motion.img
                    src={item.imageUrl}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    style={{ filter: 'none' }}
                  />
                </ListItemImage>

                <motion.h5 initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
                  {item.label}
                </motion.h5>
              </CategoriesListItem>
            ))}
          </CategoriesListWrapper>
        </PanelBody>
      </PanelInner>
    </Panel>
  );
};

export default CategoriesPanel;
