import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000, // Change to your desired port
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    minify: 'terser', // Ensure Terser is used
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log
        drop_debugger: true, // Remove debugger
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // For example, group large dependencies into separate chunks
          reactVendor: ['react', 'react-dom'],
          someOtherChunk: ['becomap', 'framer-motion', 'moment']
        }
      },
      plugins: [visualizer({ filename: 'bundle-report.html', open: true })],
    }
  }
});
