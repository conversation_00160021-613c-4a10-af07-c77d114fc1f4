import { useEffect, useRef, useState } from 'react';
import './style.css';

const SplashScreen = ({ active }) => {
  const GifPlayer = ({ gifSrc, durationMs = 3000, onComplete }) => {
    const gifRef = useRef(null);

    useEffect(() => {
      const timer = setTimeout(() => {
        if (onComplete) onComplete();
      }, durationMs);

      return () => clearTimeout(timer);
    }, [durationMs, onComplete]);

    return (
      <div>
        <img ref={gifRef} src={gifSrc} style={{ width: '300px' }} alt="animated" />
      </div>
    );
  };

  const splashRef = useRef(null);
  const preloaderRef = useRef(null);
  const glowRef = useRef(null);
  const timersRef = useRef([]);
  const [animationDone, setAnimationDone] = useState(false);
  const [gifDone, setGifDone] = useState(false);
  const [shouldFadeOut, setShouldFadeOut] = useState(false);
  const [blurLevel, setBlurLevel] = useState(10);

  useEffect(() => {
    const preloaderElement = preloaderRef.current;
    const glowElement = glowRef.current;

    const glowTimer = setTimeout(() => {
      if (glowElement) {
        glowElement.classList.add('show');
      }
    }, 300);

    const animationTimer = setTimeout(() => {
      setAnimationDone(true);

      // After animation is done but not yet active, blur to 2px
      let currentBlur = 10;
      const downToTwo = setInterval(() => {
        currentBlur -= 1;
        setBlurLevel(currentBlur);
        if (currentBlur <= 2) {
          clearInterval(downToTwo);
        }
      }, 50);
    }, 3000);

    timersRef.current = [glowTimer, animationTimer];

    return () => {
      timersRef.current.forEach(clearTimeout);
    };
  }, []);

  useEffect(() => {
    let blurToZero;
    const splashElement = splashRef.current;

    if (animationDone && active && gifDone) {
      let currentBlur = 2;
      blurToZero = setInterval(() => {
        currentBlur -= 1;
        setBlurLevel(currentBlur);
        if (currentBlur <= 0) {
          clearInterval(blurToZero);
          setShouldFadeOut(true);
          document.body.style.overflow = 'auto';
          setTimeout(() => {
            if (splashElement) splashElement.style.display = 'none';
          }, 500);
        }
      }, 50);
    }

    return () => clearInterval(blurToZero);
  }, [active, animationDone, gifDone]);

  return (
    <div
      ref={splashRef}
      className={`splash-screen ${shouldFadeOut ? 'fade-out' : ''}`}
    >
      <div
        ref={preloaderRef}
        className="preloader-img"
        style={{ filter: `blur(${blurLevel}px)` }}
      ></div>
      <div ref={glowRef} className="glow"></div>
      <div className="svg-container">
        <GifPlayer
          gifSrc="/loader-2.gif"
          durationMs={3000}
          onComplete={() => setGifDone(true)}
        />
      </div>
    </div>
  );
};

export default SplashScreen;