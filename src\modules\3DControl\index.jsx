import { useEffect, useRef, useState } from 'react';
import { RotateBoxContainer, RotateMapComponent } from '../MapController/index.styled';
import { useMap } from '../../context/MapContext';
import useDeState from '../../hooks/debounceState';

const ThreeDControl = () => {
  const { mapViewOptions, updateViewPort, isStarted } = useMap();
  const [isDragging, setIsDragging] = useState(false);
  const [currentRotationX, setCurrentRotationX] = useDeState(0, 10);
  const [currentRotationY, setCurrentRotationY] = useDeState(0, 10);
  useEffect(() => {
    // const { pitch, bearing } = mapViewOptions;
    // setCurrentRotationX(pitch);
    // setCurrentRotationY(bearing);
  }, [mapViewOptions, setCurrentRotationX, setCurrentRotationY]);
  useEffect(() => {
    const handler = setTimeout(() => {
      const { pitch, bearing } = { pitch: currentRotationX, bearing: currentRotationY };
      updateViewPort({ pitch, bearing });
      // updatePitch(pitch);
      // updateBearing(bearing);
      // todo : currently this has issue on calling both function to sdk at the same time - this function needs to be merged
    }, 10); // Delay in milliseconds

    // Cleanup the timeout if bearing/pitch changes before timeout completes
    return () => {
      clearTimeout(handler);
    };
  }, [currentRotationX, currentRotationY, updateViewPort]);
  const startX = useRef(0);
  const startY = useRef(0);

  const boxRef = useRef(null);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    startX.current = e.clientX;
    startY.current = e.clientY;
  };

  const MAX_ROTATION_X = 45; // Limit the up/down tilt
  const MAX_ROTATION_Y = 45; // Limit the left/right tilt
  const handleMouseMove = (e) => {
    if (!isDragging) return;

    const deltaX = e.clientX - startX.current;
    const deltaY = e.clientY - startY.current;

    setCurrentRotationY((prevRotationY) =>
      Math.max(-MAX_ROTATION_Y, Math.min(MAX_ROTATION_Y, prevRotationY + deltaX * 0.4))
    );

    setCurrentRotationX((prevRotationX) =>
      Math.max(-MAX_ROTATION_X, Math.min(MAX_ROTATION_X, prevRotationX - deltaY * 0.4))
    );

    startX.current = e.clientX;
    startY.current = e.clientY;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);
  const faces = ['front', 'back', 'left', 'right', 'top', 'bottom'];

  if (isStarted) return <></>;
  return (
    <RotateMapComponent>
      <RotateBoxContainer
        onMouseDown={handleMouseDown}
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
        }}
      >
        <div
          className="box"
          ref={boxRef}
          style={{
            transform: `rotateX(${currentRotationX}deg) rotateY(${currentRotationY}deg)`,
          }}
        >
          {faces.map((face, i) => (
            <div key={i} className={`face ${face}`}></div>
          ))}
        </div>
      </RotateBoxContainer>
    </RotateMapComponent>
  );
};

export default ThreeDControl;
