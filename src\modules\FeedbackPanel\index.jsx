import React, { useState } from 'react';
import {
  ContactFormDescription,
  ContactFormItem,
  ContactFormRow,
  ContactForms,
  ContactFormSingleItem,
  ContactFormTitle,
  ContactFormWrapper,
  ContactHeader,
  ItemCard,
  Panel,
  PanelInner,
  SuccessModal,
  PanelBody,
} from '../MapView/index.styled';
import Button from '@/components/shared/button/button';
import { useMap } from '../../context/MapContext';
import { captureUserDetails } from '../../shared/utils';
import { toast } from 'react-toastify';
const initialState = {
  name: { value: '', error: false },
  contactNumber: { value: '', error: false, errorMessage: '' },
  feedback: { value: '', error: false },
};
const FeedbackPanel = ({ open = false }) => {
  const siteIdentifier = import.meta.env.VITE_APP_SITE_IDENTIFIER;
  const [formState, setFormState] = useState({ ...initialState });
  const [showSuccess, setShowSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const validateFields = () => {
    let isValid = true;
    const newFormState = { ...formState };

    // Validate name
    if (newFormState.name.value.trim() === '') {
      newFormState.name.error = true;
      isValid = false;
    } else {
      newFormState.name.error = false;
    }

    // Validate contact number using regex
    const phnNumberRegex = /^[6-9]\d{9}$/;
    if (newFormState.contactNumber.value.trim() === '') {
      newFormState.contactNumber.error = true;
      newFormState.contactNumber.errorMessage = 'Contact Number is Required';
      isValid = false;
    } else if (!phnNumberRegex.test(newFormState.contactNumber.value.trim())) {
      newFormState.contactNumber.error = true;
      newFormState.contactNumber.errorMessage = 'Invalid contact number';
      isValid = false;
    } else {
      newFormState.contactNumber.error = false;
      newFormState.contactNumber.errorMessage = '';
    }

    // Validate feedback
    if (newFormState.feedback.value.trim() === '') {
      newFormState.feedback.error = true;
      isValid = false;
    } else {
      newFormState.feedback.error = false;
    }

    setFormState(newFormState);
    return isValid;
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    try {
      event.preventDefault();
      if (validateFields()) {
        const payload = {
          name: formState.name.value,
          phone: formState.contactNumber.value,
          location: formState.feedback.value,
          site_identifier: siteIdentifier,
        };
        setLoading(true);
        captureUserDetails(payload)
          .then(() => {
            setShowSuccess(true);
            toast.success('Feedback submitted successfully');
            setFormState(initialState);
            setTimeout(() => {
              setShowSuccess(false);
            }, 10000);
          })
          .catch((err) => {
            console.log({ err });
            toast.error('Something went wrong');
          }).finally(() => {
            setLoading(false);
          });
        // Proceed with form submission
      }
    } catch (error) {
      setLoading(false);
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Panel open={open}>
      <PanelInner center>
        <PanelBody center style={{ maxWidth: '480px', margin: '0 auto' }}>
          <ContactHeader>
            <h4>
              Welcome to <br /> HP
            </h4>
            <p>
              {/* <span>+91 495 712 3556</span> */}
              {/* <span>&#x2022;</span> */}
              {/* <span>www.hp.com</span> */}
            </p>
          </ContactHeader>
          <ItemCard contact>
            {showSuccess ? (
              <SuccessModal>
                <div>
                  <div className="message">Success!</div>
                  <div className="sub-message">thanks for your feedback</div>
                  <div className="loader">
                    <div className="checkmark"></div>
                  </div>
                </div>
              </SuccessModal>
            ):null}
              <ContactFormWrapper>
                <ContactForms>
                  <ContactFormRow>
                    <ContactFormItem>
                      <ContactFormSingleItem error={formState.name.error}>
                        <label>
                          Your Name <span>*</span>
                        </label>
                        <input
                          type="text"
                          value={formState.name.value}
                          onChange={(e) =>
                            setFormState({
                              ...formState,
                              name: { ...formState.name, value: e.target.value, error: false },
                            })
                          }
                          required="required"
                        />
                        {formState.name.error && <span className="error">Name is required</span>}
                      </ContactFormSingleItem>
                    </ContactFormItem>

                    <ContactFormItem>
                      <ContactFormSingleItem error={formState.contactNumber.error}>
                        <label>
                          Contact Number <span>*</span>
                        </label>
                        <input
                          type="text"
                          value={formState.contactNumber.value}
                          onChange={(e) => {
                            setFormState({
                              ...formState,
                              contactNumber: {
                                ...formState.contactNumber,
                                value: e.target.value,
                                error: false,
                                errorMessage: '',
                              },
                            });
                          }}
                          required="required"
                        />
                        {formState.contactNumber.error && (
                          <span className="error">{formState.contactNumber.errorMessage}</span>
                        )}
                      </ContactFormSingleItem>
                    </ContactFormItem>
                    <ContactFormItem>
                      <ContactFormSingleItem error={formState.feedback.error}>
                        <label>Enter Your Feedback</label>
                        <textarea
                          type="textarea"
                          value={formState.feedback.value}
                          onChange={(e) =>
                            setFormState({
                              ...formState,
                              feedback: { ...formState.feedback, value: e.target.value, error: false },
                            })
                          }
                          required="required"
                        ></textarea>
                        {formState.feedback.error && <span className="error">Feedback is required</span>}
                      </ContactFormSingleItem>
                    </ContactFormItem>
                    <ContactFormItem>
                      <ContactFormSingleItem>
                        <Button loading={loading} onClick={handleSubmit} type="submit" variant="primary">
                          Submit
                        </Button>
                      </ContactFormSingleItem>
                    </ContactFormItem>
                  </ContactFormRow>
                </ContactForms>
              </ContactFormWrapper>
            
          </ItemCard>
        </PanelBody>
      </PanelInner>
    </Panel>
  );
};

export default FeedbackPanel;
