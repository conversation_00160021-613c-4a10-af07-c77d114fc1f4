import { useState, useEffect } from 'react';

function useStateWithLocalStorage(key, initialValue) {
  // Retrieve saved value from localStorage or use the initialValue if not found
  const storedValue = localStorage.getItem(key);
  const [value, setValue] = useState(storedValue ? JSON.parse(storedValue) : initialValue);

  // Update localStorage whenever the value changes
  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(value));
  }, [key, value]);

  return [value, setValue];
}

export default useStateWithLocalStorage;
