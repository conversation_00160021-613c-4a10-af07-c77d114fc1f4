import { useSearchParams } from 'react-router-dom';
import { useMap } from '../../context/MapContext';
import { useCallback, useEffect, useState } from 'react';

const useUrlParams = () => {
  const [searchParams] = useSearchParams();
  const { allLocations } = useMap();
  const [params, setParams] = useState(false);
  const [loaded, setLoaded] = useState(false);
  // Convert URL params to an object
  const getParams = useCallback(() => {
    const paramsObj = {};

    for (let [key, value] of searchParams.entries()) {
      // Parse JSON strings to arrays/objects if needed
      try {
        paramsObj[key] = JSON.parse(value);
      } catch {
        paramsObj[key] = value;
      }
    }

    return paramsObj;
  }, [searchParams, allLocations]);
  const getLocationById = useCallback(
    (id) => {
      return allLocations.find((it) => it.id === id);
    },
    [allLocations]
  );
  useEffect(() => {
    if (loaded) return;
    const params = getParams();
    if (allLocations.length) {
      let stops = [
        {
          type: 'origin',
          edit: false,
          //   nodeId: '672c8d8daf77f178dc3a7768', //locationId
          //   node: {}, //location object,
          //   label: 'PVR', //location Name
        },
        {
          type: 'destination',
          //   nodeId: '',
          //   node: {},
          //   label: 'Lulu HyperMart',
          edit: false,
        },
      ];
      let isValid = false;
      let data = { origin: null, destination: null, waypoints: [] };
      const { from: origin, to: destination, waypoints } = params;
      if (origin) {
        data.origin = getLocationById(origin);
        stops = stops.map((it) => {
          if (it.type === 'origin') {
            return { ...it, node: data.origin.nodeId, nodeId: origin, label: data.origin.name };
          }
          return it;
        });
      }
      if (destination) {
        data.destination = getLocationById(destination);
        stops = stops.map((it) => {
          if (it.type === 'destination') {
            return { ...it, node: data.destination.nodeId, nodeId: destination, label: data.destination.name };
          }
          return it;
        });
      }
      if (waypoints) {
        waypoints.forEach((it) => {
          const location = getLocationById(it);
          if (location) {
            stops.push({ type: 'middle', node: location.nodeId, nodeId: it, label: location.name, edit: false });
          }
        });
      }
      isValid = !!stops.find((it) => it.node);
      stops = [
        ...stops.filter((item) => item.type === 'origin'), // Origin at the start
        ...stops.filter((item) => item.type === 'middle'), // Middle types in the middle
        ...stops.filter((item) => item.type === 'destination'), // Destination at the end
      ].map((item, index) => ({
        ...item,
        id: index + 1,
      }));

      setParams(isValid ? stops : false);
      setLoaded(true);
    }
  }, [searchParams, allLocations, getParams, getLocationById, loaded]);

  return params;
};

export default useUrlParams;
