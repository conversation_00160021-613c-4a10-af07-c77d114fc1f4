import styled from 'styled-components';
import Select from 'react-select';

export const MapControls = styled.div`
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
  position: absolute;
  pointer-events: none;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  * {
    pointer-events: all;
  }
`;

export const MapControlsInnerWrapper = styled.div`
  position: absolute;
  ${({ position }) => {
    switch (position) {
      case 'topRight':
        return `
                top: 20px;
                right: 20px;
            `;
      case 'topLeft':
        return `
                top: 20px;
                left: 20px;
            `;
      case 'bottomRight':
        return `
                bottom: 20px;
                right: 20px;
            `;
      case 'bottomLeft':
        return `
                bottom: 20px;
                left: 20px;
            `;
      default:
        return `
                top: 20px;
                right: 20px;
                `;
    }
  }}
`;

export const ZoomControl = styled.div`
  height: 72px;
  border-radius: 12px;
  box-shadow:
    0 -2px 20px #0000001a,
    0 0 12px #0000001a;
  @media (max-width: 734px) {
    display: none;
  }
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fdfdfd;
    border: none;
    cursor: pointer;
    padding: 3px;
    width: 100%;
    height: 50%;
    svg {
      opacity: 0.6;
    }
    &.zoom-in {
      border-radius: 12px 12px 0 0;
      border-bottom: 1px solid rgb(237, 237, 237);
      @supports ((backdrop-filter: blur(30px)) or (-webkit-backdrop-filter: blur(30px))) {
        -webkit-backdrop-filter: blur(15px);
        backdrop-filter: blur(15px);
        background-color: #fffc;
      }
    }
    &.zoom-out {
      border-radius: 0 0 12px 12px;
      @supports ((backdrop-filter: blur(30px)) or (-webkit-backdrop-filter: blur(30px))) {
        -webkit-backdrop-filter: blur(15px);
        backdrop-filter: blur(15px);
        background-color: #fffc;
      }
    }

    &:active {
      background-color: rgba(229, 229, 229, 0.6);
      svg {
        opacity: 1;
      }
    }
  }
`;

export const FocusControl = styled.div`
  overflow: hidden;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  box-shadow:
    0 -2px 20px #0000001a,
    0 0 12px #0000001a;
  margin-bottom: 12px;
  @media (max-width: 734px) {
    display: none;
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fdfdfd;
    border: none;
    cursor: pointer;
    padding: 3px;
    width: 100%;
    height: 100%;
    svg {
      opacity: 0.6;
    }
    @supports ((backdrop-filter: blur(30px)) or (-webkit-backdrop-filter: blur(30px))) {
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      background-color: #fffc;
    }
    &:active {
      background-color: rgba(229, 229, 229, 0.6);
      svg {
        opacity: 1;
      }
    }
  }
`;

export const FloorSwitcher = styled(Select)`
  .bc__control {
    border-radius: 12px;
    box-shadow:
      0 -2px 20px #0000001a,
      0 0 12px #0000001a,
      0 1px 3px 0 #00000012,
      0 0 0 2px ${({ theme }) => theme.colors.primary} inset;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    line-height: 18px;
    border: none;
    width: 190px;
    /* padding: 8px 13px; */
    @supports ((backdrop-filter: blur(30px)) or (-webkit-backdrop-filter: blur(30px))) {
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      background-color: #fffc;
    }
  }
  .bc__menu {
    @supports ((backdrop-filter: blur(30px)) or (-webkit-backdrop-filter: blur(30px))) {
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      background-color: #fffc;
    }
    width: 100%;
    border-radius: 10px;
    box-shadow:
      0 -2px 20px #0000001a,
      0 0 12px #0000001a;
    font-size: 13px;
    overflow: hidden;
    .bc__menu-list {
      padding: 6px;

    }
    .bc__option {
      cursor: pointer;
      border-radius: 8px;

      &--is-focused {
        background-color: rgba(229, 229, 229, 0.6);
      }
      &--is-selected {
        background-color: ${({ theme }) => theme.colors.primary};;
      }
    }
    .bc__group {
      padding: 0;
      position: relative;
        &:before {
          content: "";
          width: 1px;
          background-color: #cdcdcd;
          pointer-events: none;
          position: absolute;
          top: 30px;
          left: 16px;
          bottom: 18px;
        }
      .bc__group-heading {
      cursor: pointer;
        display: block;
        font-size: inherit;
        margin: 0;
        font-weight: normal;
        width: 100%;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        background-color: transparent;
        color: inherit;
        padding: 8px 12px;
        box-sizing: border-box;
        text-transform:capitalize
      }
      .bc__option {
        margin-left: 34px;
        width: calc(100% - 34px);
        position: relative;
        border-radius: 8px;
        &:before {
          content: "";
          width: 10px;
          height: 1px;
          background-color: #cdcdcd;
          pointer-events: none;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: -18px;
          bottom: 15px;
        }
       
      }
    }
  }
`;

export const RotateMapComponent = styled.div`
  overflow: hidden;
  width: 65px;
  height: 65px;
  border-radius: 12px;
  box-shadow:
    0 -2px 20px #0000001a,
    0 0 12px #0000001a;
  margin-bottom: 12px;
  position: absolute;
  bottom: 10px;
  right: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  background-image: url(/axis.png);
  background-size: 60px;
  background-repeat: no-repeat;
  background-position: center top;
  --box-size: 20px;
  @media (max-width: 734px) {
    display: none;
  }
`;
export const RotateBoxContainer = styled.div`
  width: var(--box-size);
  height: var(--box-size);
  perspective: 1000px;
  cursor: pointer;

  .box {
    position: relative;
    width: var(--box-size);
    height: var(--box-size);
    transform-style: preserve-3d;
    transition: transform 0.1s linear;
  }

  .face {
    position: absolute;
    width: var(--box-size);
    height: var(--box-size);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #555;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.5rem;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
  }

  .front {
    transform: translateZ(calc(var(--box-size) / 2));
    background: ${({ theme }) => theme.colors.primary};;
  }

  .back {
    transform: rotateY(180deg) translateZ(calc(var(--box-size) / 2));
    background: ${({ theme }) => theme.colors.primary};;
  }

  .left {
    transform: rotateY(-90deg) translateZ(calc(var(--box-size) / 2));
    background: #59d4f3;
  }

  .right {
    transform: rotateY(90deg) translateZ(calc(var(--box-size) / 2));
    background: #59d4f3;
  }

  .top {
    transform: rotateX(90deg) translateZ(calc(var(--box-size) / 2));
    background: #f8c15c;
  }

  .bottom {
    transform: rotateX(-90deg) translateZ(calc(var(--box-size) / 2));
    background: #f8c15c;
  }
`;
