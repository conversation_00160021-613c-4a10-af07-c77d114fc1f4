import { useEffect, useState } from 'react';
import {
  CategoriesList,
  CategoriesListInner,
  CategoryButton,
  CategoryImage,
  CategoryLabel,
  ExpandArrow,
} from '../MapView/index.styled';
import Icon from '@/components/shared/Icon';
import { motion } from 'framer-motion';

export const ListCategory = ({ title = '', listData = [], onSelect, limiter = null }) => {
  const [expanded, setExpanded] = useState(false);
  const [visibleCount, setVisibleCount] = useState(limiter || 5); // Initially show 5 items

  useEffect(() => {
    if (expanded) {
      // setTimeout(() => {
      //   setExpanded(false);
      // }, 3000);
    }
  }, [expanded, setExpanded]);
  const handleExpand = () => {
    if (expanded) {
      // Collapse to the initial 5 items
      setVisibleCount(limiter || 5);
      setExpanded(false);
    } else {
      // Expand gradually
      setExpanded(true);
      listData.slice(visibleCount).forEach((_, index) => {
        setTimeout(() => {
          setVisibleCount((prevCount) => prevCount + 1);
        }, index * 100); // Adjust the delay (100ms) for item addition speed
      });
    }
  };
  return (
    <>
      <CategoriesList>
        <CategoriesListInner>
          {listData.map((item, index) => (
            <CategoryButton onClick={() => onSelect(item)} key={index}>
              <CategoryImage>
                {item.logo ? (
                  <img src={item.logo} alt={item.label} width="40" height="40" />
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24"><path fill="currentColor" d="M19.717 20.362C21.143 19.585 22 18.587 22 17.5c0-1.152-.963-2.204-2.546-3C17.623 13.58 14.962 13 12 13s-5.623.58-7.454 1.5C2.963 15.296 2 16.348 2 17.5s.963 2.204 2.546 3C6.377 21.42 9.038 22 12 22c3.107 0 5.882-.637 7.717-1.638" opacity="0.5"/><path fill="currentColor" fillRule="evenodd" d="M5 8.515C5 4.917 8.134 2 12 2s7 2.917 7 6.515c0 3.57-2.234 7.735-5.72 9.225a3.28 3.28 0 0 1-2.56 0C7.234 16.25 5 12.084 5 8.515M12 11a2 2 0 1 0 0-4a2 2 0 0 0 0 4" clipRule="evenodd"/></svg>
                )}
              </CategoryImage>
              {/* <CategoryLabel>{category.label}</CategoryLabel> */}
            </CategoryButton>
          ))}
        </CategoriesListInner>
      </CategoriesList>
      {/* {limiter  && listData.length > limiter && (
        <ExpandArrow onClick={handleExpand} open={expanded}>
          <div>
            <span></span>
            <span></span>
          </div>
        </ExpandArrow>
      )} */}
    </>
  );
};

export default ListCategory;
