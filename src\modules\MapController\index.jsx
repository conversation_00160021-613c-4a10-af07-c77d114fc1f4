import { MapControls, MapControlsInnerWrapper } from './index.styled';
import ThreeDControl from '../3DControl';
import FloorSwitcherController from '../FloorSwitcher';
import FocusOnMap from '../FocusOnSite';
import ZoomController from '../ZoomControl';
import { isMobile } from 'react-device-detect';

const MapController = () => {
  return (
    <>
      <MapControls>
        <FloorSwitcherController />
        {/* <ThreeDControl /> */}
        {!isMobile ? (
          <MapControlsInnerWrapper position="bottomRight">
            <FocusOnMap />
            <ZoomController />
          </MapControlsInnerWrapper>
        ) : (
          ''
        )}
      </MapControls>
    </>
  );
};

export default MapController;
