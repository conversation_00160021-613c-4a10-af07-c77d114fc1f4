import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { groupConsecutiveDays, processOperationHours } from '../../shared/utils';
import {
  CollapseArrow,
  Hour,
  HourLabel,
  HourTime,
  LocationCollapseSection,
  LocationCollapseSectionHead,
  LocationCollapseSectionIns,
  LocationDescription,
  LocationDetailSection,
  LocationDetailSectionTitle,
  LocationFocusBody,
  LocationFocusWrapper,
  LocationHours,
  Status,
} from '../MapView/index.styled';
import Icon from '@/components/shared/Icon';

const RenderOperationHours = ({ location }) => {
  const { description } = location || {};
  const [isExpanded, setIsExpanded] = useState(false);
  const [cardExpanded, setCardExpanded] = useState(false);

  const previewLimit = 70;

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };
  const [finalList, setFinalList] = useState([]);
  const [todaysData, setTodaysData] = useState(false);
  const formatTime = (time) => moment(time, 'HH:mm').format('h:mm A');
  useEffect(() => {
    // const dayOrder = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    // let fullList = [];
    // (hoursData || []).forEach((group) => {
    //   group.dayOfWeek.forEach((it) => {
    //     fullList.push({ day: it, opens: group.opens, closes: group.closes });
    //   });
    // });
    // dayOrder.forEach((day) => {
    //   if (!fullList.find((it) => it.day === day)) {
    //     fullList.push({ day, opens: null, closes: null });
    //   }
    // });
    // fullList = fullList.map((it) => {
    //   //find is the day is today and add a flag in to it using moment
    //   return { ...it, today: moment().format('dddd') == it.day };
    // });

    // // setFullList(fullList);
    // //sort the list by day and group consecutive days (if any)
    // const sortedData = fullList.length
    //   ? groupConsecutiveDays(fullList.sort((a, b) => dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day)))
    //   : [];

    // const finalListData = sortedData
    //   .map((it) => {
    //     if (Array.isArray(it)) {
    //       let day = `${it[0].day.slice(0, 3)} - ${it[it.length - 1].day.slice(0, 3)}`;
    //       return { ...it[0], day };
    //     } else {
    //       return { ...it, day: it.day.slice(0, 3) };
    //     }
    //   })
    //   .map((it) => ({
    //     ...it,
    //     opens: it.opens ? formatTime(it.opens) : '',
    //     closes: it.closes ? formatTime(it.closes) : '',
    //   }));
    // const todaysData = fullList.find((it) => it.today);

    const { finalList, todaysData } = processOperationHours(location);
    setFinalList(finalList);
    setTodaysData(todaysData);
  }, [location]);
  // const todaysData = useMemo(() => fullList.find((it) => it.today), [fullList]);

  return (
    <>
      <LocationDetailSection onClick={() => setCardExpanded(!cardExpanded)}>
        <LocationDetailSectionTitle>Hours</LocationDetailSectionTitle>
        <LocationFocusWrapper>
          <LocationCollapseSection>
            <LocationCollapseSectionIns>
              {todaysData?.opens && todaysData?.closes ? (
                <LocationCollapseSectionHead expand={cardExpanded}>
                  {formatTime(todaysData?.opens)} - {formatTime(todaysData?.closes)}
                </LocationCollapseSectionHead>
              ) : (
                ''
              )}
              {todaysData?.opens && todaysData?.closes ? (
                <Status color={'#65c366'}>Open</Status>
              ) : (
                <Status color={'#f24933'}>Closed</Status>
              )}
            </LocationCollapseSectionIns>
          </LocationCollapseSection>
          <CollapseArrow expand={cardExpanded}>
            <Icon icon={'chevron-right'} size={16} />
          </CollapseArrow>
        </LocationFocusWrapper>
        <LocationFocusBody expand={cardExpanded}>
          <LocationHours>
            {finalList.map((it, index) => {
              return (
                <Hour key={index}>
                  <HourLabel>{it.day}</HourLabel>
                  <HourTime>
                    {it.opens ? (
                      <p>
                        <span>{it.opens}</span>
                        <span className="separator">-</span>
                        <span>{it.closes}</span>
                      </p>
                    ) : (
                      <p style={{ color: '#f24933' }}>Closed</p>
                    )}
                  </HourTime>
                </Hour>
              );
            })}
          </LocationHours>
        </LocationFocusBody>
      </LocationDetailSection>
      <LocationDetailSection>
        <LocationDescription>
          <p>
            {isExpanded || (description || '').length <= previewLimit
              ? description || ''
              : `${(description || '').slice(0, previewLimit)}`}
            {(description || '').length > previewLimit && (
              <button onClick={toggleExpand}>{isExpanded ? '. Less' : '...More'}</button>
            )}
          </p>
        </LocationDescription>
      </LocationDetailSection>
    </>
  );
};

export default RenderOperationHours;
