// MapContext.js
import { createContext, useContext, useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { getMapView, getSite } from 'becomap';
import { useSearchParams } from 'react-router-dom';
import useDeState from '../hooks/debounceState';
import { toast } from 'react-toastify';
import { fetchJsonData, removeDuplicates } from '../shared/utils';
import { isMobile } from 'react-device-detect';
import { trackEvent } from '../shared/analyticsService';

const MapContext = createContext();

export const MapProvider = ({ navigateTo, children }) => {
  const mapViewOptionEnv = import.meta.env.VITE_APP_MAP_VIEW_OPTION;
  const mapViewOptionEnvMobile = import.meta.env.VITE_APP_MAP_VIEW_OPTION_MOBILE;

  const shownSplash = import.meta.env.VITE_APP_SPLASH_SCREEN === 'true' && !localStorage.getItem('shownedSplash');
  const mapViewOption = mapViewOptionEnv ? JSON.parse(mapViewOptionEnv) : {};
  const mapViewOptionMobile = mapViewOptionEnvMobile ? JSON.parse(mapViewOptionEnvMobile) : mapViewOption;

  const [searchParams] = useSearchParams();
  const mapContainer = useRef(null);
  const mapRef = useRef(null);
  const [map, setMap] = useState({ referance: null, loading: true, error: null });
  const [siteIdentifier, setSiteIdentifier] = useState(null);
  const [allLocations, setAllLocations] = useState([]);
  const [allBuilding, setAllBuilding] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [happenings, setHappenings] = useState({ OFFER: [], NEWS: [], EVENT: [] });
  const [floorData, setFloorData] = useState({ floors: [], currentFloor: 0 });  
  const [allCategories, setAllCategories] = useState([]);
  const [allAmenities, setAllAmenities] = useState([]);
  const [amenitiesGrouped, setAmenitiesGrouped] = useState({});
  const [isStarted, setIstarted] = useState(false);
  const [defaultFloor, setDefaultFloor] = useState(null);
  const [showBottomBar, setShowBottomBar] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [userPreferences, setUserPreferences] = useState([]);
  const defaultFloorRef = useRef(null);
  const [sessionId, setSessionId] = useState(null);
  // const [eventSuggestions, setEventSuggestions] = useState([]);

  const [eventSuggestions, setEventSuggestions] = useState(() => {
    // const savedEvents = localStorage.getItem('eventSuggestions');

    // return savedEvents ? JSON.parse(savedEvents) : [];
    return [];
  });

  // useEffect(() => {
  //   localStorage.setItem('eventSuggestions', JSON.stringify(eventSuggestions));
  // }, [eventSuggestions]);

  const [mapViewOptions, setMapViewOptions] = useDeState(isMobile ? mapViewOptionMobile : mapViewOption, 1000);

  const [navigation, setNavigation] = useState(false);

  const getQueryParam = useCallback(
    (key) => {
      return searchParams.get(key); // This will return the value of the param or null if it doesn’t exist
    },
    [searchParams]
  );
  // const organisation = useMemo(() => getQueryParam('org'), [getQueryParam]);
  const updateBearing = useCallback((bearing) => {
    mapRef.current.updateBearing(bearing);
  }, []);
  const findDepartments = useCallback(async (mapRef) => {
    const baseUrl = import.meta.env.VITE_APP_BASE_URL_S3;
    let data = mapRef
      .getLocations()
      .map((it) => ({
        ...it,
        subtitle: it?.description || '',
        nodeId: it,
        logo: it.logo || null,
        id: it.id,
        topLocation: it.topLocation,
        categories: it?.categories || [],
        externalId: it?.externalId || '',
      }))
      .filter((it) => it.nodeId)
      .filter((it) => it.categories.map((it) => it.name).includes('Department'));
    for (let index = 0; index < data.length; index++) {
      try {
        if (!data[index].externalId) throw new Error("External id doesn't exist for " + data[index].name);
        const jData = await fetchJsonData(baseUrl + data[index].externalId + '.json');
        data[index].externalData = jData;
      } catch (error) {
        console.warn('warning', error);
      }
    }
    setDepartments(data);
  }, []);

  // Function to attach a one-time listener that clears itself
  // const attachSingleUseListener = (mapRef, eventName, callback) => {
  //   // Define the handler
  //   console.log('attachSingleUseListener', { eventName, mapRef });
  //   const handler = (data) => {
  //     // Call the callback with the floor data
  //     console.log({ data });
  //     callback(data);
  //     // Remove this listener after it is executed
  //     mapRef.current.eventsHandler.off(eventName, handler);
  //   };

  //   // Attach the event listener
  //   mapRef.current.eventsHandler.on(eventName, handler);
  // };

  useEffect(() => {
    const initializeMap = async () => {
      trackEvent('loaded_map');
      // if (!mapContainer.current) return;

      try {
        const siteIdentifier = getQueryParam('org');

        if (!siteIdentifier) {
          // window.location.href = '/testing';
          return;
        }
        const bottomBar = import.meta.env.VITE_APP_SHOW_BOTTOM_BAR === 'yes';
        const getSiteOptions = {
          clientId: import.meta.env.VITE_APP_CLIENT_ID,
          clientSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
          siteIdentifier: siteIdentifier,
          // siteIdentifier: '672c7dbbaf77f178dc3a7721',
        };
        const site = await getSite(getSiteOptions);

        mapRef.current = await getMapView(mapContainer?.current, site, mapViewOptions);
        mapRef.current.eventsHandler.on('load', () => {
          setTimeout(
            () => {
              setMap({ referance: mapRef.current, loading: false, error: null });
              localStorage.setItem('shownedSplash', 'true');
            },
            shownSplash ? 2100 : 0
          );
          setSiteIdentifier(siteIdentifier);
          setShowBottomBar(bottomBar);
          setAllBuilding((site?.buildings || []).map((it) => ({ ...it, floors: it.floors })));
          setFloorData((prev) => ({ ...prev, floors: site?.buildings[0]?.floors }));
          setAllLocations(
            mapRef.current
              .getLocations()
              .map((it) => ({
                ...it,
                subtitle: it?.description || '',
                nodeId: it,
                logo: it.logo || null,
                id: it.id,
                topLocation: it.topLocation,
                name: it?.name
              }))
              .filter((it) => it.nodeId)
              .filter((it) => it.type!== 'MAP_OBJECT')
          );
          setAmenitiesGrouped(
            (mapRef.current.getAllAminityLocations() || []).reduce((acc, item) => {
              // If the key doesn't exist, create an empty array
              if (!acc[item.amenity]) {
                acc[item.amenity] = [];
              }
              // Push the current item to the respective array
              acc[item.amenity].push(item);
              return acc;
            }, {})
          );
          findDepartments(mapRef.current);
          const happenings = {
            OFFER: mapRef.current.getHappenings('OFFER') || [],
            NEWS: mapRef.current.getHappenings('NEWS') || [],
            EVENT: mapRef.current.getHappenings('EVENT') || [],
          };
          happenings.OFFER = removeDuplicates(happenings.OFFER); //temp fix for duplication
          happenings.NEWS = removeDuplicates(happenings.NEWS); //temp fix for duplication
          happenings.EVENT = removeDuplicates(happenings.EVENT); //temp fix for duplication
          setHappenings(happenings);

          setAllCategories(
            (mapRef.current.getCategories() || []).map((it) => ({
              category: it,
              id: it.id,
              imageUrl: it.icon,
              label: it.name,
              color: it.color,
            }))
          );
          setAllAmenities(mapRef.current.getAmenities() || []);
          // setTimeout(() => {
          //   mapRef.current.setBounds([76.3478461924758, 10.052758382063018, 76.35288454540694, 10.057171855341068]);
          // }, 2000);
          // updateBearing(183);
          // if (siteIdentifier === '6784cd0ef0795c2c24ca8ab5') {
          // mapRef.current.resetDefaultViewport({ bearing: 183 });
          // }
          try {
            const tweakFloor = localStorage.getItem('tweakFloor') || 3;
            const floors = site?.buildings[0]?.floors;

            if (tweakFloor && floors?.length && Number(tweakFloor) < floors.length) {
              const index = Number(tweakFloor);
              const selectedFloor = floors[index];

              setTimeout(() => {
                mapRef.current.selectFloor(selectedFloor);
                setFloorData((prev) => ({
                  ...prev,
                  currentFloor: index,
                }));
                setDefaultFloor(selectedFloor);
              }, 500);
            }
          } catch (error) {
            console.log('tweak Floor', error);
          }
        });
        mapRef.current.eventsHandler.on('zoomChange', (zoom) => {
          console.log('\x1b[31m%s\x1b[0m', `Zoom changed`, zoom); //yellow
          // setMapViewOptions({ ...mapViewOptions, zoom: zoom.zoom });
        });
        mapRef.current.eventsHandler.on('pitchChange', (pitch) => {
          console.log('\x1b[31m%s\x1b[0m', `Pitch changed `, pitch); //yellow
        });
        mapRef.current.eventsHandler.on('bearingChange', (bearing) => {
          console.log('\x1b[31m%s\x1b[0m', `Bearing changed `, bearing); //yellow
        });
        mapRef.current.eventsHandler.on('select', () => {
          // console.log('Location selection', location);
        });
        mapRef.current.eventsHandler.on('switchToFloor', (floor) => {
          if (!defaultFloorRef.current) {
            defaultFloorRef.current = floor;
            setDefaultFloor(floor);
            mapRef.current.eventsHandler.off('switchToFloor');
          }
        });
        mapRef.current.eventsHandler.on('viewChange', (view) => {
          // console.log('\x1b[31m%s\x1b[0m', `View Changed `, view); //yellow
          setMapViewOptions(view?.viewOptions || {});
        });

        setQuestions(mapRef.current.getQuestions());
        mapRef.current
          .getSessionId()
          .then((sessionId) => {
            setSessionId(sessionId);
          })
          .catch((error) => {
            console.error('Error getting session ID:', error);
          });
        // mapRef.current.eventsHandler.on('walkthroughEnd', () => {
        //   console.log('Walthrough ended');
        // });

      } catch (error) {
        localStorage.setItem('error', JSON.stringify(error));
        const { statusCode, description, timeStamp, message } = error?.response?.data || {};
        trackEvent('initializing_map', { statusCode, description, timeStamp, message,error });
        setMap({ referance: null, loading: false, error });
        if (message) toast.error(message);
        // navigateTo('/error', { message });
        // window.location.reload();
        console.error('Error fetching site:', error, { statusCode, description, timeStamp, message });
      }
    };
    initializeMap();
    return () => {
      if (mapRef.current) {
        mapRef.current.eventsHandler.off('zoomChange');
        mapRef.current.eventsHandler.off('pitchChange');
        mapRef.current.eventsHandler.off('bearingChange');
        mapRef.current.eventsHandler.off('select');
        mapRef.current.eventsHandler.off('switchToFloor');
        // mapRef.current.eventsHandler.off('stepLoad');
        // mapRef.current.eventsHandler.off('walkthroughEnd');
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  console.log({ mapViewOptions });

  const tempMobileEnv = [
    {"zoom":18.460182311245383,"pitch":40,"bearing":-0.2787324360315324,"center":[17.23790198379902, 78.42954282289747]},
    {"zoom":18.460182311245383,"pitch":40,"bearing":-0.2787324360315324,"center":[17.235722690487535,78.4293133775343]},
    {"zoom":18.460182311245383,"pitch":40,"bearing":-0.2787324360315324,"center":[17.235722690487535,78.4293133775343]},
    {"zoom":18.460182311245383,"pitch":40,"bearing":-0.2787324360315324,"center":[17.235722690487535,78.4293133775343]},
    {"zoom":19,"pitch":60,"bearing":-0.2787324360315324,"center":[17.23790198379902, 78.42954282289747]},
    {"zoom":19,"pitch":60,"bearing":-0.2787324360315324,"center":[17.23790198379902, 78.42954282289747]}
  ]

  const switchFloor = useCallback(
    (index, resetToDefault = true) => {  
      mapRef.current.selectFloor(floorData.floors[index]);
      setFloorData({ ...floorData, currentFloor: index }); 
      // if (resetToDefault) resetToDefaultViewport();
      if( !Object.values(floorData?.floors[index]?.viewPort)?.every(value => value === null)){
        if(isMobile){
          updateViewPort(tempMobileEnv[index]);
        }else{
           updateViewPort(floorData?.floors[index]?.viewPort);
        }
      }else {
        resetToDefaultViewport();
      }
    },
    [floorData]
  );

  const updateZoom = useCallback((value) => {
    const newZoom = value;
    mapRef.current.setViewport({ zoom: newZoom });
  }, []);

  const updateViewPort = useCallback((value = {}) => {
    mapRef.current.setViewport(value);
  }, []);

  const updatePitch = useCallback((value) => {
    mapRef.current.updatePitch(value);
  }, []);

  const navigateUser = useCallback((data) => {
    setNavigation(data);
  }, []);

  const focusOnLocation = useCallback(
    (location) => {
      if (!map) return;
      map.referance.focusTo(location, 20);
    },
    [map]
  );
  const selectLocation = useCallback(
    (location) => {
      if (!map) return;
      console.log('triggered selectLocation', { location });
      mapRef.current.selectLocation(location);
    },
    [map]
  );

  const resetToDefaultViewport = useCallback(() => {
    mapRef.current.resetDefaultViewport();
  }, [map]);
  const showAmenity = useCallback(
    (amenityType = null) => {
      if (!map) return;
      if (!amenityType) {
        mapRef.current.clearSelection();
      } else {
        mapRef.current.selectAmenities(amenityType);
      }
    },
    [map]
  );
  const navigationStatus = useCallback((status) => {
    setIstarted(status);
  }, []);

  const clearSelection = useCallback(() => {
    mapRef.current.clearSelection();
  }, [mapRef]);

  const selectLocationsByCategory = useCallback(
    (category) => {
      const locations = allLocations
        .filter((location) => (location.categories || []).map((it) => it.id).includes(category.id))
        .map((it) => it.nodeId);
      mapRef.current.selectLocation(locations);
    },
    [allLocations, map]
  );

  const getEventSuggestions = useCallback(async(sessionId, answers) => {
    console.log('Fetching event suggestions:', answers, sessionId);
    await mapRef.current.getEventSuggestions(sessionId, answers)
      .then((events) => {
        console.log('Events', events);
        setEventSuggestions(events); // Update state after promise resolves
      })
      .catch((error) => {
        console.error('Error fetching event suggestions:', error);
      });
  }, [mapRef, setEventSuggestions]);
  const contextValue = useMemo(
    () => ({
      mapContainer,
      map,
      allLocations,
      allCategories,
      allBuilding,
      floorData,
      mapViewOptions,
      navigation,
      allAmenities,
      isStarted,
      happenings,
      departments,
      showBottomBar,
      siteIdentifier,
      amenitiesGrouped,
      defaultFloor,
      switchFloor,
      navigateUser,
      focusOnLocation,
      showAmenity,
      navigationStatus,
      updateBearing,
      updatePitch,
      updateZoom,
      updateViewPort,
      resetToDefaultViewport,
      selectLocationsByCategory,
      selectLocation,
      setQuestions,
      questions,
      userPreferences,
      setUserPreferences,
      sessionId,
      getEventSuggestions,
      eventSuggestions,
      clearSelection,
    }),
    [
      map,
      allLocations,
      allCategories,
      allBuilding,
      floorData,
      mapViewOptions,
      navigation,
      allAmenities,
      isStarted,
      happenings,
      departments,
      showBottomBar,
      siteIdentifier,
      amenitiesGrouped,
      defaultFloor,
      switchFloor,
      navigateUser,
      focusOnLocation,
      showAmenity,
      navigationStatus,
      updateBearing,
      updatePitch,
      updateZoom,
      updateViewPort,
      resetToDefaultViewport,
      selectLocationsByCategory,
      selectLocation,
      setQuestions,
      questions,
      userPreferences,
      setUserPreferences,
      sessionId,
      getEventSuggestions,
      eventSuggestions,
      clearSelection,
    ]
  );

  return <MapContext.Provider value={contextValue}>{children}</MapContext.Provider>;
};

// eslint-disable-next-line react-refresh/only-export-components
export const useMap = () => useContext(MapContext);
