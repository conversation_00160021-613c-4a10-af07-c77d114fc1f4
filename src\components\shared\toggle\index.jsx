import styled from 'styled-components';

function Toggle({
  label = '',
  value = false,
  left,
  right,
  leftColor,
  rightColor,
  leftBgColor,
  rightBgColor,
  circleColor,
  setChecked,
}) {
  return (
    <Wrapper>
      <span>{label}</span>
      <CheckBox
        left={left}
        right={right}
        leftColor={leftColor}
        rightColor={rightColor}
        leftBgColor={leftBgColor}
        rightBgColor={rightBgColor}
        circleColor={circleColor}
        checked={value}
        onChange={(value) => setChecked(value.target.checked)}
        type="checkbox"
      />
    </Wrapper>
  );
}

// checkbox wrapper
const Wrapper = styled.div`
  justify-content: center;
  align-items: center;
  display: flex;
  z-index: 0;
`;

const CheckBox = styled.input`
  z-index: 1;
  width: 5rem;
  height: 2rem;
  background: var(--second);
  background: ${(props) => props.leftBgColor ?? 'var(--grey)'};
  border-radius: 2em;
  ::before {
    position: absolute;
    content: '${(props) => props.left ?? 'Yes'}';
    width: 5rem;
    height: 2rem;
    display: flex;
    padding: 0 0 0 1em;
    justify-content: flex-start;
    align-items: center;
    color: ${(props) => props.leftColor ?? 'var(--white)'};
    font-weight: var(--bold);
    font-size: var(--small);
    transition: all 0.2s ease-in-out;
  }
  ::after {
    position: relative;
    content: '';
    display: block;
    width: 1.6em;
    height: 1.6em;
    top: calc((2rem - 1.6em) / 2);
    left: calc(5rem - 1.9em);
    border-radius: 50%;
    background: ${(props) => props.circleColor ?? 'var(--white)'};
    transition: all 0.2s ease-in-out;
  }
  &:checked {
    background: ${(props) => props.rightBgColor ?? 'var(--black)'};
    transition: all 0.2s ease-in-out;
    ::before {
      position: absolute;
      padding: 0 0 0 1.5em;
      content: '${(props) => props.right ?? 'No'}';
      align-items: center;
      justify-content: center;
      font-weight: var(--bold);
      font-size: var(--small);
      color: ${(props) => props.rightColor ?? 'var(--white)'};
    }
    ::after {
      content: '';
      z-index: 2;
      top: calc((2rem - 1.6em) / 2);
      left: calc((2rem - 1.6em) / 2);
      width: 1.6em;
      height: 1.6em;
      display: block;
      position: relative;
      border-radius: 50%;
      background: white;
    }
  }
`;

//   <Toggle left="true" right="false" leftBgColor="var(--first)" rightBgColor="red"></Toggle>
export default Toggle;
