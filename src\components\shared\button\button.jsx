// src/components/common/Button.tsx
import React, { <PERSON><PERSON>ventHandler } from 'react';
import styled, { css } from 'styled-components';

const StyledButton = styled.button`
  position: relative;
  padding: 0.75rem 1rem;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  text-decoration: none;
  border-radius: 12px;
  outline: none;
  appearance: none;
  border: none;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  height: 2.5rem;

  &:hover {
    opacity: 0.8;
  }

  ${({ variant }) =>
    variant === 'primary' &&
    css`
      background-color: ${({ theme }) => theme.colors.primary};;
      color: white;
    `}

  ${({ variant }) =>
    variant === 'grey' &&
    css`
      background-color: rgb(118 118 128 / 12%);
      color: ${({ theme }) => theme.colors.primary};;
    `}

    ${({ variant }) =>
    variant === 'dark' &&
    css`
      background-color: #0d0d0d;
      color: white;
    `}

    ${({ variant }) =>
    variant === 'danger' &&
    css`
      background-color: #ea615d;
      border: 2px solid #E55D58;
      color: white;
    `}


    
  ${({ variant }) =>
    variant === 'white' &&
    css`
      background-color: white;
      border: 2px solid #DEDEDE;
      color: #0d0d0d;
    `}
    
${({ iconOnly }) =>
    iconOnly &&
    css`
      padding: 0;
      width: 2.5rem;

      svg {
        width: 16px;
        height: 16px;
      }
    `}

    ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.5;
      cursor: not-allowed;
    `}
`;

// Define the Button component
const Button = ({
  onClick,
  type = 'button',
  className,
  children,
  variant = 'default',
  disabled = false,
  style,
  icon,
}) => {
  return (
    <StyledButton
      style={style}
      type={type}
      onClick={onClick}
      className={className}
      variant={variant}
      disabled={disabled}
      iconOnly={icon ? true : false}
    >
      {icon ? icon : children}
    </StyledButton>
  );
};

export default Button;
