import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useMap } from '../../context/MapContext';
import {
  calculateWalkingTime,
  canUpdateArray,
  clearDrawnRoute,
  clearUrlParams,
  debounce,
  drawRoute,
  groupByType,
  metersToFeet,
  searchLocations,
} from '../../shared/utils';
import {
  AddStopButton,
  Alert,
  ButtonGroupWrapper,
  DirectionIcon,
  DirectionInput,
  DirectionLabel,
  DirectionPaths,
  DirectionSection,
  DirectionStopContainer,
  DragPanHandle,
  NaviagtionSwitcher,
  NavigationTitleHead,
  NavigationTitleHeadActions,
  RouteInfo,
  RoutesButtons,
  SegmentedControl,
  StateCard,
  StateCardBody,
  StateCardHead,
  StateCardHeader,
} from '../MapView/index.styled';
import Icon from '@/components/shared/Icon';
import { ReactSortable } from 'react-sortablejs';
import Button from '@/components/shared/button/button';
import ListComponent from '../ListComponent';
import StepsComponent from './steps';
import { useSearchParams } from 'react-router-dom';
import NavigateOnMap from '../NavigateOnMap';
import useDeState from '../../hooks/debounceState';
import Share from '../../components/share';
// import { use } from 'framer-motion/client';
import { motion, steps } from 'framer-motion';
import LocationCard from '../LocationCard';
import { toast } from 'react-toastify';
import { isMobile } from 'react-device-detect';
import { directionCardSteps } from '../../shared/common';
import introJs from 'intro.js';
import { trackEvent } from '../../shared/analyticsService';
import config from '../../config/envConfig';

const initialValue = [
  { id: 1, type: 'origin' },
  { id: 2, type: 'destination' },
];

const DirectionsCard = ({ screenWidth, data, urlDirection, cancel = () => {}, setNavigationStarted }) => {
  const allsegmentsRef = useRef(false);
  const toggleSelect = useRef(false);
  const [searchParams] = useSearchParams();
  const [selectedOption, setSelectedOption] = useState('metro');

  const handleChange = useCallback((event) => {
    setAccessible(event.target.value);
  }, []);
  const {
    map: { referance: map },
    allLocations,
    focusOnLocation,
    navigationStatus,
    resetToDefaultViewport,
  } = useMap();
  const [show, setShow] = useDeState(false);
  const [search, setSearch] = useState('');
  const [lastInput, setLastInput] = useDeState(null, 100);
  const [locations, setLocations] = useState([]);
  const [dropdownOpen, setDropdownOpen] = useDeState(false);
  const [segments, setSegments] = useState(false);
  const [toggleSteps, setToggleSteps] = useState(false);
  const [isStarted, setIsStarted] = useDeState(false);
  const [stops, setStops] = useDeState([...initialValue]);
  const [accessible, setAccessible] = useState('false');
  const [isMultipleStops, setIsMultipleStops] = useState(false);
  const [onFocus, setOnFocus] = useDeState(false, 30);
  const [nowAt, setNowAt] = useDeState(false);
  const [selectedLocation, setSelectedLocation] = useDeState(false);
  const [jumpTo, setJumpTo] = useDeState(false);
  const focusOnLocationDebounce = debounce((location) => {
    try {
      focusOnLocation(location);
    } catch (e) {
      console.log(e);
    }
  }, 500);
  useEffect(() => {
    if (data || urlDirection) {
      toggleSelect.current = true;
    } else {
      toggleSelect.current = false;
    }
    return () => {
      toggleSelect.current = false;
    };
  }, [data, urlDirection]);
  useEffect(() => {
    setSelectedLocation(false);
  },[isStarted])
  useEffect(() => {
    map?.eventsHandler.on('select', (location) => {
      if (!toggleSelect.current) return;
      const find = allLocations.find((it) => it.id === location.locations[0].id);
      setSelectedLocation(find);
      focusOnLocationDebounce(find.nodeId);
    });
    return () => {
      map?.eventsHandler.off('select');
    };
  }, [data]);


  const addStop = (location = {}, editIndex = null, focus = false) => {
    let stepsData = [...stops];
    if (editIndex === null) {
      const newStop = { id: stops.length + 1, type: 'middle', ...location }; // Ensure unique ID
      stepsData.splice(stepsData.length - 1, 0, newStop);
    }
    stepsData = stepsData.map((it, index) => {
      return {
        ...it,
        id: index + 1,
        type: index === 0 ? 'origin' : index === stepsData.length - 1 ? 'destination' : 'middle',
        ...(editIndex === index ? location : {}),
      };
    });
    if (stops.length > 4) {
      toast.warn('You can add only 5 stops');
      return;
    }
    if (canUpdateArray(stepsData)) {
      setStops(stepsData);
      setTimeout(() => {
        const inputElement = document.getElementById('location-' + (stepsData.length - 2));
        if (inputElement && focus) {
          inputElement.focus();
        }
      }, 50);
    }
    // setIsMultipleStops(true);
  };

  const handleSort = (evt) => {
    // Create a new array with updated items order
    const newItems = [...stops];
    const [movedItem] = newItems.splice(evt.oldIndex, 1);
    newItems.splice(evt.newIndex, 0, movedItem);
    // Update keyToUpdate or any other property you need
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      type: index === 0 ? 'origin' : index === newItems.length - 1 ? 'destination' : 'middle',
    }));
    if (canUpdateArray(updatedItems)) {
      setStops(updatedItems);
    } else {
      setStops(stops);
    }
    // const reorderedStops = items.map((stop, index) => ({
    //   ...stop,
    //   type: index === 0 ? 'origin' : index === items.length - 1 ? 'destination' : 'middle',
    // }));
    // setStops(reorderedStops);
  };

  useEffect(() => {
    if (data) {
      const { id: nodeId, nodeId: node, name: label } = data?.arrival || {};
      const { id: depNodeId, nodeId: depNode, name: depLabel } = data?.departure || {};
      setStops((prev) =>
        prev.map((it) => {
          if (it.type === 'destination') {
            return { ...it, nodeId, node, label, edit: false };
          }
          if (it.type === 'origin') {
            return { ...it, nodeId: depNodeId, node: depNode, label: depLabel, edit: false };
          }
          return { ...it, edit: false };
        })
      );
      setShow(true);
      return;
    }
    setStops([
      { id: 1, type: 'origin' },
      { id: 2, type: 'destination' },
    ]);
    setIsMultipleStops(false);
    setShow(false);
  }, [data]);

  useEffect(() => {
    if (stops.length > 2) {
      setIsMultipleStops(true);
    } else {
      setIsMultipleStops(false);
    }
    updateUrl(stops);
  }, [stops]);

  useEffect(() => {
    if (urlDirection && Array.isArray(urlDirection)) {
      setStops(urlDirection);
      setShow(true);
      // return;
    }
  }, [setShow, setStops, urlDirection]);

  useEffect(() => {
    if (!data) {
      // clearDrawnRoute(map);
    }
  });

  const onFocusInput = (fieldIndex = null) => {
    // setTimeout(() => {
    setOnFocus(true);
    // }, 150);
    // setOnFocus(true);
    setSearch('');
    setLastInput(fieldIndex);
    setStops((prev) =>
      prev.map((it, index) => {
        if (fieldIndex === index) {
          setSearch(it?.label || '');
          return { ...it, edit: true };
        }
        return { ...it, edit: false };
      })
    );
  };
  const onBlurInput = (fieldIndex = null) => {
    setTimeout(() => {
      setOnFocus(false);
    }, 1);
    setStops((prev) =>
      prev.map((it, index) => {
        if (fieldIndex === index) {
          return { ...it, edit: false };
        }
        return { ...it };
      })
    );
    if (locations.length || allLocations.length) {
      setLastInput(fieldIndex);
    } else {
      setLastInput(null);
      setLocations([]);
    }
    setSearch(''); //need testing to check if there is any bug
  };
  const swapDestination = () => {
    let stepsData = [...stops];
    stepsData = stepsData.reverse().map((it, index) => {
      return {
        ...it,
        id: index + 1,
        type: index === 0 ? 'origin' : index === stepsData.length - 1 ? 'destination' : 'middle',
      };
    });
    setStops(stepsData);
    setLastInput(null);
  };

  useEffect(() => {
    if (!map) return;
    if (!search) {
      // setLocations([]);
      return;
    }
    const searchdata = async () => {
      const data = await searchLocations(map, search);
      setLocations(
        (data || [])
          .map((it) => {
            const { id, name, description, logo, polygonId, ...other } = it;
            return {
              name,
              subtitle: description,
              logo: logo || '',
              nodeId: it,
              polygonId,
              ...other,
              id,
            };
          })
          .filter((it) => it.nodeId).filter((it) => it.type!== 'MAP_OBJECT')
      );
    };
    searchdata();
  }, [map, search]);
  useEffect(() => {
    if (show && isStarted) {
      navigationStatus(true);
    } else {
      navigationStatus(false);
    }
    return () => {
      navigationStatus(false);
    };
  }, [show, isStarted, navigationStatus]);

  // eslint-disable-next-line no-unused-vars
  const stateWithoutEditFlag = useMemo(() => stops.map(({ edit, selected, chosen, ...rest }) => rest), [stops]);

  useEffect(() => {
    drawRouteOnMap();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(stateWithoutEditFlag), accessible]);

  const drawRouteOnMap = useCallback(() => {
    try {
      // console.log('drawRouteOnMap--???');
      if (!map) return;
      clearDrawnRoute(map);
      const { origin, destination, middle } = groupByType(stops);
      setSegments(false);
      if (!origin || !destination) {
        // resetToDefaultViewport();
        return;
      }
      if (!canUpdateArray(stops, false)) {
        // console.log('canUpdateArray');
        return setSegments(false);
      }
      drawRoute(map, origin, destination, middle || [], accessible === 'true').then((segments) => {
        //   if (segments && segments.length > 0) {
        //     map.routeController.walkThrough(segments[0], (step) => {
        //
        //     });
        //   }
        if (!segments) {
          // toast.error('Path not found');
          setSegments(false);
          return;
        }
        allsegmentsRef.current = (segments || [])
          .map((it, indexSeg) => {
            return it.steps.map((it) => {
              return {
                segmentIndex: indexSeg,
                stepIndex: it.orderIndex,
                step: it,
              };
            });
          })
          .flat();
        setSegments(segments || []);
      });
      //
    } catch (e) {
      toast.error('Some error occured');
    }
  }, [accessible, stops, map]);
  const updateUrl = (stops) => {
    // clearUrlParams();
    const result = {
      origin: null,
      destination: null,
      waypoints: [],
    };

    stops.forEach((item) => {
      if (item.type === 'origin') {
        result.origin = item.nodeId;
      } else if (item.type === 'destination') {
        result.destination = item.nodeId;
      } else if (item.type === 'middle') {
        result.waypoints.push(item.nodeId);
      }
    });

    const url = new URL(window.location.href);
    url.searchParams.set('org', searchParams.get('org'));
    result?.origin ? url.searchParams.set('from', result.origin) : url.searchParams.delete('from');
    result?.destination ? url.searchParams.set('to', result.destination) : url.searchParams.delete('to');
    result?.waypoints?.length
      ? url.searchParams.set('waypoints', JSON.stringify(result.waypoints))
      : url.searchParams.delete('waypoints');
    // if(!(result?.origin||result?.destination)){
    //   url.searchParams.delete('waypoints');
    // }
    window.history.pushState({}, '', url);
  };
  const startWalkThrough = (index = 0) => {
    if (segments && segments.length) {
      setIsStarted(true);
      map.routeController.walkThrough(segments[index], (step, isLastStep) => {
        if (isLastStep) {
          setTimeout(() => {
            if (index <= segments.length - 1) {
              startWalkThrough(index + 1);
            } else {
              setNowAt(false);
              setIsStarted(false);
              setNavigationStarted(false);
            }
          }, 500);
        }
        setNowAt(step);
      });
    }
  };
  const stopWalkThrough = useCallback(() => {
    map.routeController.stopWalking();
    map.routeController.showStep(segments[0].steps[0]);
    setIsStarted(false);
    setNowAt(false);
  }, [map, segments]);

  const resetStates = useCallback(() => {
    setShow(false);
    setSearch('');
    setLastInput(null);
    setLocations([]);
    setDropdownOpen(false);
    setToggleSteps(false);
    setIsStarted(false);
    setIsMultipleStops(false);
    setStops([...initialValue]);
    setOnFocus(false);
    setNavigationStarted();
    allsegmentsRef.current = false;
    setTimeout(() => {
      setSegments(false);
    }, 200);
  }, []);
  const clearLocation = useCallback(
    (index) => {
      setStops((prev) => {
        //remove some keys from object where index is equal to index
        return prev
          .map((it, i) => {
            if (i === index) {
              if (it.type === 'middle') {
                return undefined;
              } else {
                return {
                  id: it.id,
                  type: it.type,
                  edit: it.edit,
                };
              }
            }
            return it;
          })
          .filter((it) => it);
      });
    },
    [setStops, stops]
  );

  const goBack = () => {
    clearUrlParams();
    clearDrawnRoute(map);
    cancel();
    resetStates();
    resetToDefaultViewport();
  };
  const distance = useMemo(() => {
    const distances = (segments || []).map((it) => it.distance);
    const distance = distances.reduce((prev, curr) => {
      return prev + curr;
    }, 0);
    return Math.ceil(distance ? Math.round(distance * 100) / 100 : 0);
  }, [segments]);
  const duration = useMemo(() => {
    const distances = (segments || []).map((it) => it.distance);
    const distance = distances.reduce((prev, curr) => {
      return prev + curr;
    }, 0);
    return distance ? calculateWalkingTime(distance) : '';
  }, [segments]);
  const enableAddStop = useMemo(() => (stops || []).every((it) => it.node), [stops]);
  const lastIncomplete = useMemo(() => {
    const data = stops || [];
    // Iterate through the array to find the last incomplete object
    let lastIncomplete = null;

    for (let i = data.length - 1; i >= 0; i--) {
      const { label, nodeId, node } = data[i];

      // Check if any of the required fields is missing
      if (!label || !nodeId || !node) {
        lastIncomplete = { index: i, type: data[i].type };
        break; // Stop at the last incomplete object
      }
    }

    return lastIncomplete; // Returns the index and type of the last incomplete object or null if all are complete
  }, [stops]);
  const dynamicHeight = useMemo(() => {
    if (data) {
      return screenWidth <= 763 ? 'fit-content' : 'auto'; // When there is a search query
    }    
    if (toggleSteps) {
      return screenWidth <= 763 ? '100dvh' : '100%'; // When focused
    } else {
      return screenWidth <= 763 ? 'fit-content' : 'auto'; 
    }
  }, [data, toggleSteps]);
  useEffect(() => {
    if (show && !urlDirection) {
      try {
        document.getElementById('location-0').focus();
      } catch (error) {
        console.log({ error });
      }
    }
  }, [show, urlDirection]);
  useEffect(() => {
    if (!config.TUTORIAL || (segments||[]).length === 0 || localStorage.getItem('tourCompletedDirectionCard')) return;
  
    const intro = introJs();
    const steps = directionCardSteps.map(step => ({
      element: document.querySelector(step.target),
      intro: step.content,
      // position: step.position || 'bottom',
    }));
  
    intro.setOptions({
      steps: steps,
      showBullets: true,
      exitOnOverlayClick: false,
      keyboardNavigation: false,
      doneLabel: 'Finish',
      nextLabel: 'Next',
      prevLabel: 'Previous',
      hidePrev: true
    });
  
   
  
    intro.onexit(() => {
      localStorage.setItem('tourCompletedDirectionCard', true);
    });
  
    intro.start();
  
    return () => {
      intro.exit();
    };
  }, [map,segments]);
  const navigationStartAnalytics=()=>{
    try {
      trackEvent('navigation_started', { stops: stops.map((it) => it.label) });
    } catch (error) {
      console.log({ error });
    }
  }
  if (!show) return <></>;
  return (
    <>
      {isStarted && (
        <NavigateOnMap
          jumpTo={jumpTo?.nowAt || false}
          initialSegment={jumpTo?.segmentIndex ?? 0}
          initialStep={jumpTo?.stepIndex ?? 0}
          stopNavigation={stopWalkThrough}
          segments={segments}
          allSegments={allsegmentsRef.current}
        />
      )}
      {!isStarted && (
        <>
          <motion.div
            initial={{ height: dynamicHeight }}
            animate={{ height: dynamicHeight }}
            transition={{ type: 'spring', stiffness: 100, damping: 20, duration: 0.3 }}
          >
            <StateCard direction>
              {selectedLocation ? (
                <LocationCard
                  startDirection={() => {
                    const find = allLocations.find((it) => it.id === selectedLocation.id);
                    const { id: nodeId, nodeId: node, name: label } = find || {};
                    addStop({ nodeId, node, label }, lastIncomplete?.index ?? null);
                    setSelectedLocation(null);
                  }}
                  page="direction-card"
                  location={selectedLocation}
                  cancelCard={() => {
                    setSelectedLocation(null);
                  }}
                  extras={{ lastIncomplete }}
                />
              ) : (
                <>
                  {!toggleSteps ? (
                    <>
                      <StateCardHeader>
                        <NavigationTitleHead>
                          <button onClick={goBack}>
                            <Icon icon={'x'} size={'22'} />
                          </button>
                          <NavigationTitleHeadActions>
                            {/* <SegmentedControl>
                              <input
                                id="two-1"
                                name="two"
                                type="radio"
                                value={'false'}
                                checked={accessible === 'false'}
                                onChange={handleChange}
                              />
                              <label htmlFor="two-1">
                                <Icon icon={'man'} size={'22'} />
                              </label>

                              <input
                                id="two-2"
                                name="two"
                                type="radio"
                                value={'true'}
                                checked={accessible === 'true'}
                                onChange={handleChange}
                              />
                              <label htmlFor="two-2">
                                <Icon icon={'wheelchair'} size={'20'} />
                              </label>
                            </SegmentedControl> */}
                            {/* <h2>Directions</h2> */}
                            {segments && segments.length && <Share />}
                          </NavigationTitleHeadActions>
                          {/* <ToggleSwitch
                    label="Accessible"
                    value={accessible}
                    setChecked={(value) => {
                      setAccessible(value);
                    }}
                  /> */}
                        </NavigationTitleHead>
                        <DirectionStopContainer>
                          <DirectionPaths>
                            {stops.map((stop, index) => (
                              <DirectionIcon key={stop.id} hsdot={index !== stops.length - 1}>
                                <Icon
                                  icon={index === stops.length - 1 ? 'marker-pin-03' : 'circle'}
                                  size={index === stops.length - 1 ? '16' : '12'}
                                />
                              </DirectionIcon>
                            ))}
                          </DirectionPaths>

                          <ReactSortable
                            className="direction-step1"
                            handle=".drag-handle"
                            list={stops}
                            style={{ width: '100%' }}
                            setList={setStops}
                            swap={true}
                            animation={150}
                            swapclassName="highlight"
                            onEnd={handleSort}
                            delayOnTouchStart={true}
                            delay={2}
                            ghostclassName="sortable-ghost"
                            chosenclassName="sortable-chosen"
                            fallbackOnBody={true}
                            fallbackTolerance={5}
                            forceFallback={true}
                            disabled={stops.length < 2}
                          >
                            {stops.map((stop, index) => {
                              //
                              const { label, edit } = stop;
                              return (
                                <DirectionSection key={'Direction-item-' + index} type={stop.type}>
                                  <DirectionLabel>
                                    <DirectionInput
                                      id={'location-' + index}
                                      value={edit ? search : label || ''}
                                      placeholder={
                                        label
                                          ? label
                                          : stop.type === 'origin'
                                            ? 'I am at'
                                            : stop.type === 'destination'
                                              ? 'I want to go to'
                                              : 'Add Stop'
                                      }
                                      autoComplete="off"
                                      autoCapitalize="off"
                                      role="combobox"
                                      aria-autocomplete="list"
                                      onFocus={() => {
                                        // setTimeout(() => {
                                        onFocusInput(index);
                                        // }, 200);
                                      }}
                                      onBlur={() => {
                                        onBlurInput(index);
                                      }}
                                      {...(edit
                                        ? {
                                            onChange: ({ target: { value } }) => {
                                              setSearch(value || '');
                                            },
                                          }
                                        : { readOnly: true })}
                                    />
                                  </DirectionLabel>
                                  {label || stop?.type === 'middle' ? (
                                    <div
                                      style={{ margin: '10px' }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        clearLocation(index);
                                        resetToDefaultViewport();
                                      }}
                                      className="close-icon"
                                    >
                                      <Icon icon="x" size={16} />
                                    </div>
                                  ) : (
                                    ''
                                  )}
                                  {isMultipleStops && (
                                    <DragPanHandle className="drag-handle">
                                      <Icon icon={'menu-01'} size={16} />
                                    </DragPanHandle>
                                  )}
                                </DirectionSection>
                              );
                            })}
                          </ReactSortable>

                          {!isMultipleStops && (
                            <NaviagtionSwitcher>
                              <Icon onClick={swapDestination} icon={'switch-vertical-01'} size="12px" />
                            </NaviagtionSwitcher>
                          )}
                        </DirectionStopContainer>
                        {stops.length < 5 && enableAddStop ? (
                          <div>
                            <AddStopButton className='direction-step2' onClick={() => addStop({}, null, true)}>
                              <Icon icon={'plus-circle'} size={'14'} />
                              <p>Add Stop</p>
                            </AddStopButton>
                          </div>
                        ) : (
                          ''
                        )}
                      </StateCardHeader>

                      {search || (lastInput !== null && onFocus) ? (
                        <StateCardBody>
                          <StateCardHead style={{ marginTop: '10px' }}>
                            {/* <h5>{search ? 'Location' : 'Top Locations'}</h5> */}
                            <h5>{'Locations'}</h5>
                          </StateCardHead>
                          <ListComponent
                            // listData={(search ? locations : allLocations.filter((it) => it.topLocation)).filter((it) => {
                            //   const filterIds = stops.map((it) => it.nodeId).filter((it) => it);
                            //   return !filterIds.includes(it.id);
                            // })}
                            listData={(search ? locations : allLocations)
                              .sort((a, b) => {
                                return b.topLocation - a.topLocation;
                              })
                              .filter((it) => {
                                //Todo - this condition is to filter out already added items
                                return it;
                                // const filterIds = stops.map((it) => it.nodeId).filter((it) => it);
                                // return !filterIds.includes(it.id);
                              })}
                            onSelect={(item) => {
                              const find = allLocations.find((it) => it.id === item.id);
                              const { id: nodeId, nodeId: node, name: label } = find || {};
                              setTimeout(() => {
                                setStops((prev) => {
                                  const newStops = prev.map((it, index) => {
                                    if (index === lastInput) {
                                      return { ...it, nodeId, node, label, edit: false };
                                    }
                                    return { ...it, edit: false };
                                  });
                                  if (!canUpdateArray(newStops)) return prev;
                                  return newStops;
                                });
                                setLastInput(null);
                                setSearch('');
                              }, 10);
                              // setSelectedLocation(find);
                            }}
                          />
                        </StateCardBody>
                      ) : (
                        ''
                      )}

                      {!onFocus && segments?.length && screenWidth > 734 ? (
                        <ButtonGroupWrapper>
                          {/* <Alert>
                      <div>
                        <Icon icon="info-hexagon" size={'22'} />
                      </div>
                      <p>There is no path connecting Calgary Duty Free and Calgary Duty Free</p>
                    </Alert> */}
                          <RouteInfo>
                            <h3>{duration}</h3>
                            <p>{Math.ceil(metersToFeet(distance))} ft</p>
                          </RouteInfo>

                          <RoutesButtons>
                            {screenWidth > 734 && (
                              <Button
                                className='direction-step3'
                                onClick={() => {
                                  setToggleSteps((prev) => !prev);
                                  if(!toggleSteps){
                                    trackEvent('navigation_used_steps', { stops: stops.map((it) => it.label) });
                                  }
                                }}
                                type="button"
                                variant="grey"
                                style={{ flex: 1, gap: '12px', alignItems: 'center', justifyContent: 'center' }}
                              >
                                Steps
                              </Button>
                            )}
                            <Button
                              className='direction-step4'
                              onClick={() => {
                                if (isStarted) {
                                  // stopWalkThrough();
                                  setNavigationStarted(false);
                                } else {
                                  // startWalkThrough();
                                  setIsStarted(true);
                                  setNavigationStarted(true);
                                  navigationStartAnalytics();
                                }
                              }}
                              type="button"
                              variant="primary"
                              style={{
                                flex: 1,
                                gap: '12px',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              {isStarted ? 'Stop' : 'Start'} <Icon icon="navigation-pointer-01" size={'16'} />
                            </Button>
                          </RoutesButtons>
                        </ButtonGroupWrapper>
                      ) : (
                        <>
                          {!segments?.length && stops.every((stop) => stop.label) && (
                            <Alert>
                              <div>
                                <Icon icon="info-hexagon" size={22} />
                              </div>
                              <p>
                              No route found between{' '}
                                {(() => {
                                  try {
                                    let data = stops.map((it) => it.label).join(' and ');
                                    return data;
                                  } catch (error) {
                                    console.error(error);
                                    return 'the selected locations';
                                  }
                                })()}
                              </p>
                            </Alert>
                          )}
                        </>
                      )}
                    </>
                  ) : (
                    <StepsComponent
                      routePlan={stops}
                      onBack={() => {
                        if (segments?.[0].steps?.[0]) {
                          // map.routeController.showStep(segments[0].steps[0]);
                        }
                        map.updateZoom(18);
                        setToggleSteps(false);
                      }}
                      onSelect={(step, stepIndex, segmentIndex) => {
                        // console.log({ step, stepIndex, segmentIndex });
                        map.routeController.showStep(step);
                        if(isMobile){
                          setJumpTo({ nowAt: step, segmentIndex, stepIndex });
                          setIsStarted(true);
                          setNavigationStarted(true);
                        }
                      }}
                      segments={segments}
                    />
                  )}
                </>
              )}
            </StateCard>
          </motion.div>
          {!isStarted && !onFocus && !toggleSteps && segments?.length && screenWidth < 734 ? (
            <StateCard
              style={{
                position: 'fixed',
                bottom: '0',
                left: '0',
                right: '0',
                top: 'auto',
                height: 'auto',
                zIndex: 12,
              }}
            >
              <ButtonGroupWrapper>
                <RouteInfo>
                  <h3>{duration}</h3>
                  <p>{Math.ceil(metersToFeet(distance))}ft</p>
                </RouteInfo>

                <RoutesButtons
                  style={{
                    justifyContent: screenWidth <= 734 ? 'flex-end' : 'center',
                  }}
                >
                  <Button
                    className='direction-step3'
                    onClick={() => {
                      setToggleSteps((prev) => !prev);
                      if(!toggleSteps){
                        trackEvent('navigation_used_steps', { stops: stops.map((it) => it.label) });
                      }
                    }}
                    type="button"
                    variant="grey"
                    style={{ flex: 1, gap: '12px', alignItems: 'center', justifyContent: 'center' }}
                  >
                    Steps
                  </Button>
                  <Button
                  className='direction-step4'
                    onClick={() => {
                      if (isStarted) {
                        setNavigationStarted(false);
                      } else {
                        setIsStarted(true);
                        setNavigationStarted(true);
                        navigationStartAnalytics();
                      }
                    }}
                    type="button"
                    variant="primary"
                    style={{
                      flex: 1,
                      gap: '12px',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    {isStarted ? 'Stop' : 'Start'} <Icon icon="navigation-pointer-01" size={'16'} />
                  </Button>
                </RoutesButtons>
              </ButtonGroupWrapper>
            </StateCard>
          ) : (
            ''
          )}
        </>
      )}
    </>
  );
};
export default React.memo(DirectionsCard);
