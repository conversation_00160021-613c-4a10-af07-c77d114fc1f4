import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Analytics from 'analytics';
import googleAnalytics from '@analytics/google-analytics';
import { useMap } from '../../context/MapContext';
import { isAnalyticsEnabled, trackEvent } from '../../shared/analyticsService';

const analytics = Analytics({
  app: 'meitra-hospital',
  plugins: [
    googleAnalytics({
      measurementIds: [import.meta.env.VITE_APP_GOOGLE_ANALYTICS_ID || ''],
    }),
  ],
});

const TrackLocationAnalytics = () => {
  const { allLocations } = useMap();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    try {
      if (!isAnalyticsEnabled()) return;
      const hasLocation = searchParams.has('location');
      const analyticsCalled = sessionStorage.getItem('analytics_location_called');
      if (hasLocation && !analyticsCalled) {
        const locationId = searchParams.get('location');
        const find = (allLocations || []).find((it) => it.id === locationId);
        if (find) {
          trackEvent(find?.name);
          sessionStorage.setItem('analytics_location_called', 'true');
        }
      }
    } catch (error) {
      console.log('Analytics error', error);
    }
  }, [searchParams, allLocations]);

  return null;
};

export default TrackLocationAnalytics;
