import { useEffect, useMemo, useState } from 'react';
import {
  NavigationTitleHead,
  ShareButton,
  StateCardBody,
  StateCardHeader,
  StepDetail,
  StepEndPoint,
  StepHeadDetails,
  StepHeadImg,
  StepImage,
  StepInfo,
  StepItem,
  StepsContainer,
  StepStartPoint,
  StepsWrapper,
  StepTitle,
} from '../MapView/index.styled';
import Icon from '@/components/shared/Icon';
import { directionDetails, directionImage, isLessThanOrEqual, processSegments, metersToFeet } from '../../shared/utils';
import Share from '../../components/share';

const StepsComponent = ({ segments = [], onSelect = () => {}, onBack = () => {}, routePlan = [] }) => {
  useEffect(() => {}, [segments, routePlan]);
  const [nowAt, setNowAt] = useState('0-0');

  const processedSegments = useMemo(() => processSegments(routePlan), [routePlan]);

  return (
    <>
      <StateCardHeader>
        <NavigationTitleHead>
          <button onClick={onBack}>
            <Icon icon={'arrow-narrow-left'} size={'22'} />
          </button>
          <h2>Route Details</h2>
          <ShareButton>
            <Share steps/>
          </ShareButton>
        </NavigationTitleHead>
      </StateCardHeader>
      <StateCardBody>
      {(segments || []).map((it, index) => {
        const { from, to } = processedSegments[index] || {};
        console.log({from,to  });
        const steps = (it?.steps || []).slice(1, -1); // Removes the first and last items
        
        return (
          <div key={index}>
            {index === 0 && (
              <StepStartPoint style={{ cursor: 'pointer' }}>
                <StepHeadImg style={{ cursor: 'pointer', backgroundColor: from?.node?.logo ? '' : from?.node?.categories?.[0]?.color?.hex }}>
                  <img src={from?.node?.logo || from?.node?.categories?.[0]?.icon} alt="" />
                </StepHeadImg>
                <StepHeadDetails
                  onClick={() => {
                    onSelect(it?.steps[0],0,index);
                    setNowAt(`${index+1}-0`);
                  }}
                >
                  <h3>{from?.label}</h3>
                  <p>{from?.node?.floor?.shortName}</p>
                </StepHeadDetails>
              </StepStartPoint>
            )}
            <StepsContainer>
              <StepsWrapper>
                {steps.map((step, stepIndex) => {
                  // const { action, direction } = step;
                  const actualIndex = `${index + 1}-${stepIndex}`;
                  const isActive = isLessThanOrEqual(actualIndex, nowAt);
                  // const isActive = true;
                  return (
                    <StepItem
                      completed={false}
                      active={isActive}
                      onClick={() => {
                        onSelect(step,stepIndex+1,index);

                        setNowAt(actualIndex);
                      }}
                      key={`${index}-${stepIndex}`}
                    >
                      <StepImage>
                        <img src={directionImage(step, it.steps, index)} alt="" />
                      </StepImage>
                      <StepInfo>
                        <StepTitle>{directionDetails(step, stepIndex, steps, { from, to })}</StepTitle>
                        {/* <StepTitle>Wayanad Vythiri Wayanad, 673591 Kerala India</StepTitle> */}
                        <StepDetail>{Math.ceil(metersToFeet(step.distance))} ft </StepDetail>
                      </StepInfo>
                    </StepItem>
                  );
                })}
                <StepItem
                  active={isLessThanOrEqual(`${index + 1}-${steps.length }`, nowAt)}
                  onClick={() => {
                    onSelect(it.steps[it.steps.length - 1],steps.length,index);
                    setNowAt(`${index + 1}-${steps.length }`);
                  }}
                  key={`${index}-${steps.length}`}
                >
                  <StepImage>
                    <img src={'/default-direction.png'} alt="" />
                  </StepImage>
                  <StepInfo>
                    <StepTitle>Arrive at {to?.label}</StepTitle>
                    {/* <StepTitle>Wayanad Vythiri Wayanad, 673591 Kerala India</StepTitle> */}
                    <StepDetail></StepDetail>
                  </StepInfo>
                </StepItem>
              </StepsWrapper>
            </StepsContainer>
            <StepEndPoint style={{ cursor: 'pointer' }}>
              <StepHeadImg style={{ cursor: 'pointer', backgroundColor: to?.node?.logo ? '' : to?.node?.categories?.[0]?.color?.hex }}>
                <img src={to?.node?.logo || to?.node?.categories?.[0]?.icon} alt="" />
              </StepHeadImg>
              <StepHeadDetails
                onClick={() => {
                  onSelect(it?.steps[it.steps.length - 1],it.steps.length - 1,index);
                  setNowAt(`${index+1}-${it.steps.length - 1}`);
                }}
              >
                <h3>{to?.label}</h3>
                <p>{to?.node?.floor?.shortName}</p>
              </StepHeadDetails>
            </StepEndPoint>
          </div>
        );
      })}
      </StateCardBody>
    </>
  );
};

export default StepsComponent;
