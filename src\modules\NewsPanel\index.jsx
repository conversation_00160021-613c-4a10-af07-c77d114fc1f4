import moment from "moment-timezone";
import { useMap } from '../../context/MapContext';
import {
  ButtonGroup,
  DepartmentDetails,
  EventDate,
  EventDateGroup,
  EventMonth,
  Flex,
  ItemCard,
  ItemCardIns,
  ListItem,
  ListItemSegment,
  ListItemTitleUnit,
  Panel,
  PanelBody,
  PanelHeader,
  PanelInner,
  SearchArea,
  TimelineContainer,
  TimelineContent,
  TimelineItem,
  TimelineTime,
} from '../MapView/index.styled';
import React, { useMemo, useState } from 'react';
import ExpandableText from '../../components/ExpandableText';
import EmptyCard from '../../components/shared/EmptyCard';
import Button from '@/components/shared/button/button';
import { useEffect } from 'react';
import { Icon as Iconify } from '@iconify/react';
import SpeakersSection from '../../components/speakerSection';
import { trackEvent } from "../../shared/analyticsService";

const eventsDates = [
  { date: '2025-03-15', data: 'No event', disabled: true },
  { date: '2025-03-16', data: 'No event', disabled: true },
  { date: '2025-03-17', data: 'Event on March 17', disabled: true },
  { date: '2025-03-18', data: 'Event on March 18', disabled: false },
  { date: '2025-03-19', data: 'Event on March 19', disabled: false },
  { date: '2025-03-20', data: 'Event on March 20', disabled: true },
  { date: '2025-03-21', data: 'No event', disabled: true },
  { date: '2025-03-22', data: 'No event', disabled: true },
];

const NewsPanel = ({ open = false, onNavigate }) => {
  const { happenings, allLocations } = useMap();
  const { EVENT } = happenings;
  const [search, setSearch] = useState('');
  const [activeDateId, setActiveDateId] = useState(eventsDates[3].date);

  const getLocation = (id) => {
    return allLocations.find((it) => it.id === id);
  };

  const [filteredNews, setFilteredNews] = useState(EVENT || []);

  useEffect(() => {
    setFilteredNews(EVENT || []);
  }, [EVENT]);

  useEffect(() => {
    eventsDates
      .filter((item) => !item.disabled)
      .forEach((eventDate) => {
        if (moment.utc(eventDate.date, 'YYYY-MM-DD').tz("Asia/Kolkata").isSame(moment.utc().tz("Asia/Kolkata"), 'day')) {
          setActiveDateId(moment.utc().tz("Asia/Kolkata").format('YYYY-MM-DD'));
          return true;
        }
      });
  }, []);

  const idFilteredData = useMemo(() => {
    return filteredNews?.filter((item) => {
      if (moment.utc(item.startDate, 'YYYY-MM-DD').tz("Asia/Kolkata").isSame(moment.utc(activeDateId, 'YYYY-MM-DD').tz("Asia/Kolkata"), 'day')) {
        return true;
      } else {
        return false;
      }
    });
  }, [filteredNews, activeDateId]);

  const searchFilteredData = useMemo(() => {
    if (!search) return idFilteredData;
    return EVENT?.filter((item) => {
      const regex = new RegExp(search, 'i');
      // Filter the array based on the `name` property
      return regex.test(item.name);
    });
  }, [search, idFilteredData, EVENT]);
  const onClickDate = (item) => {
    setActiveDateId(item.date);
  };

  const navigateToMap = (locid) => {
    onNavigate(locid);
  };
  return (
    <Panel open={open}>
      {EVENT?.length === 0 ? (
        <div>No Events found</div>
      ) : (
        <>
          <PanelInner>
            {filteredNews?.length > 0 ? (
              <PanelHeader>
                {/* {console.log(idFilteredData.length, filteredNews, 'news...')} */}
                <SearchArea>
                  <span>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <rect
                        opacity="0.5"
                        x="17.0365"
                        y="15.1223"
                        width="8.15546"
                        height="2"
                        rx="1"
                        transform="rotate(45 17.0365 15.1223)"
                        fill="currentColor"
                      ></rect>
                      <path
                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                        fill="currentColor"
                      ></path>
                    </svg>
                  </span>
                  <input
                    value={search}
                    onChange={({ target: { value } }) => setSearch(value || '')}
                    type="text"
                    name="search"
                    placeholder="Search..."
                  />
                </SearchArea>
              </PanelHeader>
            ) : null}
            {filteredNews?.length > 0 ? (
              <PanelBody>
                {!search && (
                  <Flex align="center" justify="center" gap={'12px'} style={{ marginBottom: '20px' }}>
                    {eventsDates.map((eventdate) => {
                      return (
                        <EventDateGroup
                          key={eventdate.date}
                          active={
                            moment.utc(eventdate.date, 'YYYY-MM-DD').tz("Asia/Kolkata").isSame(moment.utc(activeDateId, 'YYYY-MM-DD').tz("Asia/Kolkata"), 'day')
                              ? true
                              : false
                          }
                          onClick={() => onClickDate(eventdate)}
                          disabled={eventdate.disabled}
                        >
                          <EventMonth>{moment.utc(eventdate.date).tz("Asia/Kolkata").format('MMM')}</EventMonth>
                          <EventDate>{moment.utc(eventdate.date).tz("Asia/Kolkata").format('DD')}</EventDate>
                        </EventDateGroup>
                      );
                    })}
                  </Flex>
                )}
                {searchFilteredData?.length > 0 ? (
                  <TimelineContainer>
                    {Object.entries(
                      searchFilteredData
                        ?.sort((a, b) => moment.utc(a.startDate).tz("Asia/Kolkata").diff(moment.utc(b.startDate).tz("Asia/Kolkata")))
                        .reduce((acc, item) => {
                          const { startDate, endDate } = item;
                          const startMoment  = moment.utc(startDate).tz("Asia/Kolkata");
                          const endMoment = moment.utc(endDate).tz("Asia/Kolkata");
                          // Format start and end times in 12-hour format with AM/PM
                          const startTime = startMoment.format('h:mm A');
                          const endTime = endMoment.format('h:mm A');
                          const date = startMoment.format('DD MMMM YYYY');
                          if (acc[`${startTime} - ${endTime} - ${date}`]) {
                            acc[`${startTime} - ${endTime} - ${date}`].push(item);
                          } else {
                            acc[`${startTime} - ${endTime} - ${date}`] = [item];
                          }
                          return acc;
                        }, {})
                    )
                      // .sort((a, b) => {
                      //   const startTimeA = moment.utc(a[0].split(' - ')[0], 'h:mm A').tz("Asia/Kolkata"); // Extract start time
                      //   const startTimeB = moment.utc(b[0].split(' - ')[0], 'h:mm A').tz("Asia/Kolkata");
                      //   return startTimeA - startTimeB; // Compare timestamps
                      // })
                      .sort((a, b) => {
                        const dateA = moment.utc(a[0].split(' - ')[2], 'DD MMMM YYYY').tz("Asia/Kolkata");
                        const dateB = moment.utc(b[0].split(' - ')[2], 'DD MMMM YYYY').tz("Asia/Kolkata");
                        return dateA - dateB;
                      })
                      .map((item) => {
                        const [timeline, data] = item;
                        const timelineKey = `${timeline}-${data[0]?.id}`; // Assuming items have unique IDs
                        return (
                          <TimelineItem key={timelineKey}>
                            <TimelineTime
                            style={{
                              top: "-16px",
                              zIndex: 11,
                              background: "#efefef",
                              width: "100%",
                              backgroundColor: "#efefef",
                              alignItems: "flex-start",
                              position: "sticky",
                            }}
                            >
                              <span>{timeline}</span>
                            </TimelineTime>
                            <TimelineContent style={{ paddingRight: '0' }}>
                              {data
                                .sort((a, b) => a.name.localeCompare(b.name, undefined, { sensitivity: 'base' }))
                                .map((item) => {
                                  const { id,name, description, startDate, endDate, customFields, locationId } = item;
                                  const location = getLocation(item.locationId) || null;
                                  return (
                                    <NewsCard
                                      key={id}
                                      name={name}
                                      description={description}
                                      startDate={startDate}
                                      endDate={endDate}
                                      customFields={customFields}
                                      locationId={locationId}
                                      navigateToMap={navigateToMap}
                                      location={location}
                                      timeline={timeline}
                                    />
                                  );
                                })}
                            </TimelineContent>
                          </TimelineItem>
                        );
                      })}
                  </TimelineContainer>
                ) : (
                  <div
                    style={{
                      textTransform: 'uppercase',
                      fontWeight: 700,
                      fontSize: '20px',
                      opacity: 0.3,
                      textAlign: 'center',
                      marginTop: '30%',
                    }}
                  >
                    No Events found
                  </div>
                )}
              </PanelBody>
            ) : (
              <EmptyCard />
            )}
          </PanelInner>
        </>
      )}
    </Panel>
  );
};

const NewsCard = ({ name, description, startDate, endDate, locationId, customFields, navigateToMap, location, timeline }) => {
  const getTimeDuration = (startDate, endDate) => {
    if (!startDate && !endDate) {
      return;
    }
    const startMoment = moment.utc(startDate).tz("Asia/Kolkata");
    const endMoment = moment.utc(endDate).tz("Asia/Kolkata");

    // Format start and end times in 12-hour format with AM/PM
    const startTime = startMoment.format('h:mm A');
    const endTime = endMoment.format('h:mm A');

    // Return the formatted duration string
    return `${startTime} to ${endTime}`;
  };

  return (
    <ItemCard>
      <ItemCardIns>
        <Flex align="center" gap={'12px'} style={{ borderBlockEnd: '1px solid #e6e6e6', padding: '14px' }}>
          <ListItem>
            <ListItemSegment>
              <ListItemTitleUnit>
                <p
                  className="list-title"
                  style={{
                    fontSize: '18px',
                    marginBottom: '0',
                  }}
                >
                  {name}
                </p>
              </ListItemTitleUnit>
            </ListItemSegment>
            {description.trim() !== '' && (
              <DepartmentDetails>
                <ExpandableText previewLimit={70}>{description || ''}</ExpandableText>
              </DepartmentDetails>
            )}
            {/* <Flex justify="space-between" style={{ width: '100%' }} dir="column" gap={'8px'}> */}
              {location && (
                <Flex
                  align="center"
                  style={{
                    border: '2px solid hsla(0, 0%, 0%, 0.07)',
                    padding: '5px 6px',
                    borderRadius: '8px',
                  }}
                >
                  <div
                    style={{
                      marginRight: '4px',
                      padding: '0',
                      flex: '0 0 auto',
                      width: 'auto',
                      marginTop: '-3px',
                      color: '#34a853',
                    }}
                  >
                    <Iconify icon="solar:map-point-bold-duotone" width="18" height="18" />
                  </div>
                  <div
                    style={{
                      fontSize: '13px',
                      textAlign: 'left',
                      fontWeight: '600',
                      lineHeight: '15px',
                    }}
                  >
                    {location?.name}
                  </div>
                </Flex>
              )}
              {/* <Flex
                align="center"
                style={{
                  border: '2px solid hsla(0, 0%, 0%, 0.07)',
                  padding: '4px 6px',
                  borderRadius: '8px',
                }}
              >
                <div
                  style={{
                    marginRight: '4px',
                    padding: '0',
                    flex: '0 0 auto',
                    width: 'auto',
                    color: '#34a853',
                    marginTop: '-2px',
                  }}
                >
                  <Iconify icon="solar:clock-circle-bold-duotone" width="18" height="18" />
                </div>
                <div
                  style={{
                    fontSize: '13px',
                    fontWeight: '600',
                    textAlign: 'left',
                    lineHeight: '15px',
                  }}
                >
                  {getTimeDuration(startDate, endDate)}
                </div>
              </Flex> */}
            {/* </Flex> */}
            {customFields?.Speaker?.data && <SpeakersSection data={customFields?.Speaker?.data} />}
          </ListItem>
        </Flex>
        {location && (
          <ButtonGroup
            style={{
              padding: '7px 10px',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              type="button"
              variant="primary"
              onClick={() => {
                navigateToMap(locationId);
                trackEvent('event_navigate', {
                  location: location?.name || '',
                  event: name,
                  timeline,
                });
              }}
              style={{
                padding: '5px 21px',
                minHeight: 'auto',
                height: 'auto',
                fontSize: '14px',
                borderRadius: '10px'
              }}
            >
              Navigate
            </Button>
          </ButtonGroup>
        )}
      </ItemCardIns>
    </ItemCard>
  );
};
export default React.memo(NewsPanel);
