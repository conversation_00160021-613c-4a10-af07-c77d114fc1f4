import { useEffect, useState,useCallback } from 'react';
import { useMap } from '../../context/MapContext';
import { FloorSwitcher, MapControlsInnerWrapper } from '../MapController/index.styled';
import { trackEvent } from '../../shared/analyticsService';

const FloorSwitcherController = () => {
  const {
    floorData: { floors },
    switchFloor,
    map,
    isStarted,
    defaultFloor
  } = useMap();
  const [isActive, setIsActive] = useState(null);
  const [isGroundFloorAero,setIsGroundFloorAero] = useState(false);
 
  
  // useEffect(() => {
  //   if (defaultFloor?.floor?.shortName) setIsActive(defaultFloor?.floor?.shortName);
  // }, [defaultFloor]);
  useEffect(() => {
    map?.referance?.eventsHandler.on('switchToFloor', (floor) => {
      if(isGroundFloorAero){
        setIsActive('Aeroplaza - 1');
      }
        
         
      // }else{
      //    setIsActive(floor?.floor?.shortName);
      // }
    });
  }, [map]);

  // Map Aeroplaza floors with custom display names
  const aeroplazaFloors = floors
    .filter(floor => floor.shortName?.includes('Aeroplaza'))
    .map(floor => {
      const originalIndex = floors.indexOf(floor);
      let customLabel = floor.shortName;

      if (floor.shortName === 'Aeroplaza - 1') {
        customLabel = 'Ground Floor';
      } else if (floor.shortName === 'Aeroplaza - 2') {
        customLabel = 'First Floor';
      }

      return {
        value: originalIndex,
        label: customLabel
      };
    });

  const nonGroupedFloors = floors
    .filter(floor => !floor.shortName?.includes('Aeroplaza'))
    .map(floor => ({
      value: floors.indexOf(floor),
      label: floor.shortName
    }));

  const floorOptions = [
    ...nonGroupedFloors,
    {
      label: 'Aeroplaza',
      options: aeroplazaFloors
    }
  ];

  const handleFloorChange = (selectedOption) => {
    const selected = Array.isArray(selectedOption) ? selectedOption[0] : selectedOption;
    const selectedFloor = floors[selected.value];
    if(selectedOption?.label === "Ground Floor"){
      setIsGroundFloorAero(true); 
       setIsActive("Aeroplaza - 1");
      switchFloor(0, false);
      map.referance.setViewport( {"zoom":20,"pitch":60,"bearing":-0.2787324360315324,"center":[17.23751265409497, 78.4294969292722]},);
      trackEvent('floor_switched', {
      floor: "Aeroplaza - 1",
      floorIndex: 0
    });
    }else{
        setIsGroundFloorAero(false);
        setIsActive(selectedFloor?.shortName);
    switchFloor(selected.value, false);
    trackEvent('floor_switched', {
      floor: selectedFloor?.shortName,
      floorIndex: selected.value
    });
    }
  
  };

  const activeFloorOption = [...nonGroupedFloors, ...aeroplazaFloors]
    .find(option => option.label === (isActive === 'Aeroplaza - 1' ? 'Ground Floor'
                                : isActive === 'Aeroplaza - 2' ? 'First Floor'
                                : isActive));

  console.log(activeFloorOption,"active");
                                

  return (
    <>
      {!isStarted ? (
        <MapControlsInnerWrapper position="topRight" className='step5'>
          <FloorSwitcher
            isSearchable={false}
            options={floorOptions}
            onChange={handleFloorChange}
            defaultValue={activeFloorOption || {value: 3, label: 'Departures'}}
            value={activeFloorOption}
            formatOptionLabel={(option, { context }) => {
              if (context === 'value') {
                if (option.label === 'Ground Floor') return 'Aeroplaza - Ground';
                if (option.label === 'First Floor') return 'Aeroplaza - First';
              }
              return option.label;
            }}
            classNamePrefix="bc"
          />
        </MapControlsInnerWrapper>
      ) : (
        ''
      )}
    </>
  );
};

export default FloorSwitcherController;
