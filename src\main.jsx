import { createRoot } from 'react-dom/client';
import { ThemeProvider } from 'styled-components';
import theme from '@/config/theme.js';
import App from '@/App.jsx';
import GlobalStyle from './Global.styled';
import { MapProvider } from './context/MapContext';
import { BrowserRouter } from 'react-router-dom';
import { Slide, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

createRoot(document.getElementById('root')).render(
  <ThemeProvider theme={theme}>
    <GlobalStyle />
    <BrowserRouter>
      <App />
      <ToastContainer
        position="bottom-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick={false}
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
        transition={Slide}
      />
    </BrowserRouter>
  </ThemeProvider>
);
