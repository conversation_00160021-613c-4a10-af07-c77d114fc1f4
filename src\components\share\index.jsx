import { AnimatePresence } from 'framer-motion';
import React, { useState } from 'react';
import { ShareModal, ShareModalIns, TopActionBtn } from '../../modules/MapView/index.styled';
import { copyURLToClipboard } from '../../shared/utils';
import Icon from '@/components/shared/Icon';

const Share = ({ color, steps = false, callback = false }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const toggleDropdown = () => setDropdownOpen(!dropdownOpen);

  return (
    <>
      {steps ? (
        <Icon
          onClick={() => {
            const callThis = callback || copyURLToClipboard;
            callThis();
          }}
          icon={'share-07'}
          size={'19'}
        />
      ) : (
        <TopActionBtn
          style={{ cursor: 'pointer' }}
          onClick={() => {
            const callbackFunction = callback || copyURLToClipboard;
            callbackFunction();
          }}
        >
          <Icon icon={'share-07'} size={'15'} color={color} />
        </TopActionBtn>
      )}
      <AnimatePresence>
        {dropdownOpen && (
          <ShareModal
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <span onClick={toggleDropdown}>Close</span>
            <ShareModalIns>
              <ul>
                {/* <li>Share Link</li> */}
                <li onClick={copyURLToClipboard}>Copy Link</li>
                {/* <li>More Options</li> */}
              </ul>
            </ShareModalIns>
          </ShareModal>
        )}
      </AnimatePresence>
    </>
  );
};

export default React.memo(Share);
