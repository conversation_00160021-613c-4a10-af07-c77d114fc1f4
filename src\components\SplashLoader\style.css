.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 76px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: rgb(255, 255, 255); */
  z-index: 1000;
  flex-direction: column;
  transition:
    opacity 0.5s ease-out,
    visibility 0.5s ease-out;
  opacity: 1;
  visibility: visible;

  &.fade-out {
    opacity: 0;
    visibility: hidden;
  }

  .blurred {
  filter: blur(2px);
  transition: filter 0.5s ease;
}

  svg {
    max-width: 320px;
  }
  .preloader-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: url('/111.png') center/cover no-repeat;
    z-index: -2;
    filter: blur(10px);
    opacity: 1;
    transition:
      opacity 2s ease,
      filter 2s ease;

    &.hide {
      filter: blur(0px);
      opacity: 0;
    }

    &.idle {
      filter: blur(2px);
      /* optionally reduce brightness or use backdrop-filter */
    }
  }

  .glow {
    height: 520px;
    width: 520px;
    background-color: #ffffff;
    filter: blur(100px);
    border-radius: 150px;
    position: absolute;
    z-index: -1;

    opacity: 0;
    transition: opacity 1s ease;

    &.show {
      opacity: 0.9;
    }
  }
}

.splash-screen.lg-show {
  svg {
    animation: splashLogo 1s ease-in-out 0s 1 normal forwards;
  }
}

.splash-screen.lg-hide {
  svg {
    animation: splashLogoBack 1s ease-in-out 0s 1 normal forwards;
  }
}

@keyframes splashLogo {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-100px);
  }
}

@keyframes splashLogoBack {
  0% {
    transform: translateY(-100px);
  }

  100% {
    transform: translateY(0);
  }
}

/***************************************************
 * Generated by SVG Artista on 6/27/2025, 5:50:32 PM
 * MIT license (https://opensource.org/licenses/MIT)
 * W. https://svgartista.net
 **************************************************/

svg .svg-elem-1 {
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 0.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 0.8s;
}

svg.active .svg-elem-1 {
}

svg .svg-elem-2 {
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 0.9s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 0.9s;
}

svg.active .svg-elem-2 {
}

svg .svg-elem-3 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.6000000000000015s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.6000000000000015s;
}

svg.active .svg-elem-3 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-4 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.1s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.1s;
}

svg.active .svg-elem-4 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-5 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.2000000000000002s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.2000000000000002s;
}

svg.active .svg-elem-5 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-6 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.3s;
}

svg.active .svg-elem-6 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-7 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.4000000000000001s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.4000000000000001s;
}

svg.active .svg-elem-7 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-8 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.5s;
}

svg.active .svg-elem-8 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-9 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.6s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.6s;
}

svg.active .svg-elem-9 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-10 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.7000000000000002s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.7000000000000002s;
}

svg.active .svg-elem-10 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-11 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.8s;
}

svg.active .svg-elem-11 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-12 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.9000000000000001s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 1.9000000000000001s;
}

svg.active .svg-elem-12 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-13 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2s;
}

svg.active .svg-elem-13 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-14 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.1s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.1s;
}

svg.active .svg-elem-14 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-15 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.2s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.2s;
}

svg.active .svg-elem-15 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-16 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.3s;
}

svg.active .svg-elem-16 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-17 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.4000000000000004s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.4000000000000004s;
}

svg.active .svg-elem-17 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-18 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.5s;
}

svg.active .svg-elem-18 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-19 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.6s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.6s;
}

svg.active .svg-elem-19 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-20 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.7s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.7s;
}

svg.active .svg-elem-20 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-21 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.8s;
}

svg.active .svg-elem-21 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-22 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.9000000000000004s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 2.9000000000000004s;
}

svg.active .svg-elem-22 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-23 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3s;
}

svg.active .svg-elem-23 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-24 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.1000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.1000000000000005s;
}

svg.active .svg-elem-24 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-25 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.2s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.2s;
}

svg.active .svg-elem-25 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-26 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.3s;
}

svg.active .svg-elem-26 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-27 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.4000000000000004s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.4000000000000004s;
}

svg.active .svg-elem-27 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-28 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.5s;
}

svg.active .svg-elem-28 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-29 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.6000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.6000000000000005s;
}

svg.active .svg-elem-29 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-30 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.7s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.7s;
}

svg.active .svg-elem-30 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-31 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.8s;
}

svg.active .svg-elem-31 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-32 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.9000000000000004s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 3.9000000000000004s;
}

svg.active .svg-elem-32 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-33 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4s;
}

svg.active .svg-elem-33 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-34 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.1000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.1000000000000005s;
}

svg.active .svg-elem-34 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-35 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.2s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.2s;
}

svg.active .svg-elem-35 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-36 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.3s;
}

svg.active .svg-elem-36 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-37 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.4s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.4s;
}

svg.active .svg-elem-37 {
  fill: url('#linear-gradient');
}

svg .svg-elem-38 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.5s;
}

svg.active .svg-elem-38 {
  fill: url('#linear-gradient-2');
}

svg .svg-elem-39 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.6000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.6000000000000005s;
}

svg.active .svg-elem-39 {
  fill: url('#linear-gradient-3');
}

svg .svg-elem-40 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 0s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 0s;
}

svg.active .svg-elem-40 {
  fill: rgb(35, 65, 142);
}

svg .svg-elem-41 {
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.8s;
}

svg.active .svg-elem-41 {
}

svg .svg-elem-42 {
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.9s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 4.9s;
}

svg.active .svg-elem-42 {
}

svg .svg-elem-43 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5s;
}

svg.active .svg-elem-43 {
  fill: rgb(128, 129, 132);
}

svg .svg-elem-44 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.1s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.1s;
}

svg.active .svg-elem-44 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-45 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.2s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.2s;
}

svg.active .svg-elem-45 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-46 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.3s;
}

svg.active .svg-elem-46 {
  fill: rgb(236, 28, 36);
}

svg .svg-elem-47 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.4s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.4s;
}

svg.active .svg-elem-47 {
  fill: rgb(246, 146, 30);
}

svg .svg-elem-48 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.5s;
}

svg.active .svg-elem-48 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-49 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.6000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.6000000000000005s;
}

svg.active .svg-elem-49 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-50 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.7s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.7s;
}

svg.active .svg-elem-50 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-51 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.8s;
}

svg.active .svg-elem-51 {
  fill: rgb(0, 57, 116);
}

svg .svg-elem-52 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.9s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 5.9s;
}

svg.active .svg-elem-52 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-53 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6s;
}

svg.active .svg-elem-53 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-54 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.1000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.1000000000000005s;
}

svg.active .svg-elem-54 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-55 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.2s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.2s;
}

svg.active .svg-elem-55 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-56 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.3s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.3s;
}

svg.active .svg-elem-56 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-57 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.4s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.4s;
}

svg.active .svg-elem-57 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-58 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.5s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.5s;
}

svg.active .svg-elem-58 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-59 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.6000000000000005s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.6000000000000005s;
}

svg.active .svg-elem-59 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-60 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.7s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.7s;
}

svg.active .svg-elem-60 {
  fill: rgb(0, 170, 196);
}

svg .svg-elem-61 {
  fill: transparent;
  -webkit-transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.8s;
  transition: fill 0.7s cubic-bezier(0.23, 1, 0.32, 1) 6.8s;
}

svg.active .svg-elem-61 {
  fill: rgb(0, 170, 196);
}

.language-selector {
  position: fixed;
  bottom: -100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #fff;
  border-radius: 10px;
  box-shadow:
    0 0.156rem 0.156rem #00194b0d,
    0 0 0.5rem #00194b0d,
    0 0.313rem 0.313rem #00194b0d;
  padding: 30px;
  @media (max-width: 768px) {
    right: 15px;
    left: 15px;
  }

  h4 {
    text-align: center;
    font-size: 18px;
    margin-bottom: 26px;
  }

  .language-options {
    width: 200px;

    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      width: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;

      li {
        padding: 16px 0;
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        &:last-child {
          padding-bottom: 0;
        }

        &:first-child {
          border-bottom: 1px solid #d3dae0;
        }

        .btn-beco-nv {
          width: 100%;
          height: 38px;
        }
        &.mal {
          font-family: $font-family-malayalam;
        }
      }
    }
  }

  @supports (-webkit-appearance: none) or (-moz-appearance: none) {
    input[type='radio'] {
      --active: #275efe;
      --active-inner: #fff;
      --focus: 2px rgba(39, 94, 254, 0.3);
      --border: #bbc1e1;
      --border-hover: #275efe;
      --background: #fff;
      --disabled: #f6f8ff;
      --disabled-inner: #e1e6f9;
      -webkit-appearance: none;
      -moz-appearance: none;
      height: 18px;
      outline: none;
      display: inline-block;
      vertical-align: top;
      position: relative;
      margin: 0;
      cursor: pointer;
      border: 1px solid var(--bc, var(--border));
      background: var(--b, var(--background));
      transition:
        background 0.3s,
        border-color 0.3s,
        box-shadow 0.2s;

      &:after {
        content: '';
        display: block;
        left: 0;
        top: 0;
        position: absolute;
        transition:
          transform var(--d-t, 0.3s) var(--d-t-e, ease),
          opacity var(--d-o, 0.2s);
      }

      &:checked {
        --b: var(--active);
        --bc: var(--active);
        --d-o: 0.3s;
        --d-t: 0.6s;
        --d-t-e: cubic-bezier(0.2, 0.85, 0.32, 1.2);
      }

      &:disabled {
        --b: var(--disabled);
        cursor: not-allowed;
        opacity: 0.9;

        &:checked {
          --b: var(--disabled-inner);
          --bc: var(--border);
        }

        & + label {
          cursor: not-allowed;
        }
      }

      &:hover {
        &:not(:checked) {
          &:not(:disabled) {
            --bc: var(--border-hover);
          }
        }
      }

      &:focus {
        box-shadow: 0 0 0 var(--focus);
      }

      &:not(.switch) {
        width: 18px;

        &:after {
          opacity: var(--o, 0);
        }

        &:checked {
          --o: 1;
        }
      }

      & + label {
        font-size: 15px;
        line-height: 18px;
        display: inline-block;
        vertical-align: top;
        cursor: pointer;
        margin-left: 14px;
        font-weight: 500;
        flex: 1;
        text-align: center;
        margin-bottom: 0;
      }
    }

    input[type='radio'] {
      border-radius: 50%;

      &:after {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: var(--active-inner);
        opacity: 0;
        transform: scale(var(--s, 0.7));
      }

      &:checked {
        --s: 0.5;
      }
    }
  }
}

.lg-show {
  .language-selector {
    animation: splashSelector 1s ease-in-out 0s 1 normal forwards;
  }
}

.lg-hide {
  .language-selector {
    animation: splashSelectorBack 1s ease-in-out 0s 1 normal forwards;
  }
}

@keyframes splashSelector {
  0% {
    bottom: -100%;
  }

  100% {
    bottom: 20px;
  }
}

@keyframes splashSelectorBack {
  0% {
    bottom: 20px;
  }

  100% {
    bottom: -100%;
  }
}
