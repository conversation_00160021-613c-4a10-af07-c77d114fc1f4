import { useState, useEffect, useMemo } from 'react';
import moment from 'moment';
import { groupConsecutiveDays } from '../../shared/utils';

const useFormattedHours = (location) => {
  const { operationHours: hoursData } = location || {};
  const [finalList, setFinalList] = useState([]);
  const [fullList, setFullList] = useState([]);

  const formatTime = (time) => moment(time, 'HH:mm').format('h:mm A');

  useEffect(() => {
    const dayOrder = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    let fullListData = [];

    // Populate fullListData with day and time information
    (hoursData || []).forEach((group) => {
      group.dayOfWeek.forEach((it) => {
        fullListData.push({ day: it, opens: group.opens, closes: group.closes });
      });
    });

    // Add missing days
    dayOrder.forEach((day) => {
      if (!fullListData.find((it) => it.day === day)) {
        fullListData.push({ day, opens: null, closes: null });
      }
    });

    // Mark today's day and sort data
    fullListData = fullListData.map((it) => ({
      ...it,
      today: moment().format('dddd') === it.day,
    }));

    setFullList(fullListData);

    // Sort and group consecutive days
    const sortedData = fullListData.length
      ? groupConsecutiveDays(fullListData.sort((a, b) => dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day)))
      : [];

    // Format final list
    const finalListData = sortedData
      .map((it) => {
        if (Array.isArray(it)) {
          const day = `${it[0].day.slice(0, 3)} - ${it[it.length - 1].day.slice(0, 3)}`;
          return { ...it[0], day };
        } else {
          return { ...it, day: it.day.slice(0, 3) };
        }
      })
      .map((it) => ({
        ...it,
        opens: it.opens ? formatTime(it.opens) : '',
        closes: it.closes ? formatTime(it.closes) : '',
      }));

    setFinalList(finalListData);
  }, [hoursData]);

  // Compute today's data
  const todaysData = useMemo(() => fullList.find((it) => it.today), [fullList]);

  return { finalList, todaysData };
};

export default useFormattedHours;
