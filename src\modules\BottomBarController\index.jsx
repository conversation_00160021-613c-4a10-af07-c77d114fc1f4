import { useMap } from '../../context/MapContext';
import { BottomBar, BottomBarInner, NavItem } from '../MapView/index.styled';
import bottomBarConfig from './bottomBarConfig';

const BottomBarController = ({ activePanel, onSelect }) => {
  const { options } = bottomBarConfig;
  const { isStarted } = useMap();
  if (isStarted) return <></>;
  return (
    <BottomBar>
      <BottomBarInner count={options?.length || 5}>
        {options.map(({ key, label, icon: IconComponent, className }) => (
          <NavItem key={key} onClick={() => onSelect(key)} active={activePanel === key} order={2} className={className}>
            <span>
              <IconComponent active={activePanel === key} />
            </span>
            <p>{label}</p>
          </NavItem>
        ))}
      </BottomBarInner>
    </BottomBar>
  );
};

export default BottomBarController;
