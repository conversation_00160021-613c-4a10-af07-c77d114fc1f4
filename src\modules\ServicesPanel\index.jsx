import { useState } from 'react';
import { useMap } from '../../context/MapContext';
import { formatString } from '../../shared/utils';
import {
  CategoriesListItem,
  CategoriesListWrapper,
  ListItemImage,
  Panel,
  PanelBody,
  PanelHeader,
  PanelInner,
  PanelTitle,
  TopActionBtn,
} from '../MapView/index.styled';
import { motion } from 'framer-motion';
import Icon from '@/components/shared/Icon';

const ServicePanel = ({ open = false, onNavigate }) => {
  const iconBaseUrl = import.meta.env.VITE_APP_BASE_URL_ICON;
  const { allAmenities, amenitiesGrouped, showAmenity } = useMap();
  const [list, setList] = useState(null);
  const [selected, setSelected] = useState(null);

  const onSelect = (amenity) => {
    // showAmenity(amenity);
    setSelected(amenity);
    setList(amenitiesGrouped[amenity]);

    // Scroll smoothly to the header
    const headerElement = document.querySelector('#panel-header');
    if (headerElement) {
      headerElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const onBack = () => {
    setList(null);
    setSelected(null);
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <Panel id="service-panel" open={open}>
       <PanelInner>
      <PanelHeader id="panel-header">
        <PanelTitle>
          {list ? (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                gap: '10px',
                width: '100%',
                padding: '0 16px',
              }}
            >
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <TopActionBtn onClick={onBack}>
                  <Icon icon={'arrow-left'} size={'20'} color={' #000'} />
                </TopActionBtn>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                style={{ flex: 1, textAlign: 'center' }}
              >
                {selected ? formatString(selected) : ''}
              </motion.div>
            </div>
          ) : (
            'Utilities & Services'
          )}
        </PanelTitle>
      </PanelHeader>
      <PanelBody>
        {list ? (
          <motion.div
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={{
              hidden: { opacity: 0 },
              visible: { opacity: 1, transition: { staggerChildren: 0.1 } },
            }}
          >
            <CategoriesListWrapper>
              {list.map((item, index) => (
                <CategoriesListItem
                  onClick={() => {
                    onBack();
                    onNavigate?.({ ...item });
                  }}
                  isList
                  key={index}
                >
                  <motion.div variants={itemVariants}>
                    <h5>{item?.name}</h5>
                    <p>{item?.floor?.shortName}</p>
                  </motion.div>
                  <TopActionBtn
                    style={{
                      width: '20px',
                      height: '20px',
                    }}
                  >
                    <Icon icon={'chevron-right'} size={'16'} color={' #000'} />
                  </TopActionBtn>
                </CategoriesListItem>
              ))}
            </CategoriesListWrapper>
          </motion.div>
        ) : (
          <CategoriesListWrapper>
            {allAmenities.map((item) => (
              <CategoriesListItem onClick={() => onSelect(item)} key={item}>
                <ListItemImage style={{ padding: '10px' }}>
                  <motion.img
                    src={`${iconBaseUrl}${item.toLowerCase().replace('_', '-')}.svg`}
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                </ListItemImage>

                <motion.h5 initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
                  {formatString(item)}
                </motion.h5>
              </CategoriesListItem>
            ))}
          </CategoriesListWrapper>
        )}
      </PanelBody>
      </PanelInner>
    </Panel>
  );
};

export default ServicePanel;
