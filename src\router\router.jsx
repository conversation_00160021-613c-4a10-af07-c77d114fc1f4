import { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import MapPage from '../pages/mapper';
import ErrorPage from '../pages/error';
import { Loader } from '../components/loader';
import { useMap } from '../context/MapContext';

// Lazy loading components
const PrivateRoute = lazy(() => import('./privateRouter'));
// const MapPage = lazy(() => import('../pages/mapper'));
// const ErrorPage = lazy(() => import('../pages/error'));

const RouterComponent = () => {
  return (
    // <Router>
    <Suspense fallback={<Loader />}>
      <Routes>
        {/* Protect the root route with PrivateRoute */}
        <Route element={<PrivateRoute />}>
          <Route path="/" element={<MapPage />} />
        </Route>
        {/* Public error page route */}
        <Route path="/error" element={<ErrorPage />} />
        {/* Wildcard Route */}
        <Route path="*" element={<Navigate to="/error" replace />} />
      </Routes>
    </Suspense>
    // </Router>
  );
};

export default RouterComponent;
