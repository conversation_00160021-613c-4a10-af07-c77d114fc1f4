import moment from 'moment';

const EventsBox = ({ events=[] }) => {
  const formatDate = (dateData) => {
    return moment(dateData).format('DD-MM-YYYY HH:mm:ss');
  };

  return (
    <div>
      <h3>Events</h3>
      {events?.map(({ title, description, start_time, end_time }, i) => {
        const startDateTime = formatDate(start_time);
        const endDateTime = formatDate(end_time);
        return (
          <div key={i}>
            <p>{title}</p>
            <p>Start time - {startDateTime}</p>
            <p>End time - {endDateTime}</p>
            <p>{description}</p>
          </div>
        );
      })}
    </div>
  );
};

export default EventsBox;
