import { getRoute } from 'becomap';
import moment from 'moment';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import Analytics from 'analytics';
import googleAnalytics from '@analytics/google-analytics';

export const drawRoute = async (map, departure = '', arrival = '', waypoints = [], getAccessiblePath = false) => {
  let segments = null;
  try {
    if (!map || !departure || !arrival) {
      // console.info('invalid arguments-shared/utils/drawRoute')
      return false;
    }
    // const startNode = map.getNode(departure);
    // const endNode = map.getNode(arrival);
    const startNode = departure;
    const endNode = arrival;

    // const waypointsList = waypoints.map(it => map.getNode(it));
    const waypointsList = waypoints.map((it) => it);
    const routerOptions = {
      maxDistanceThreshold: 60,
      getAccessiblePath: getAccessiblePath,
    };

    segments = getRoute(startNode, endNode, waypointsList, routerOptions);

    if (segments) {
      map.routeController.showRoute(segments);
    }
    return segments;
  } catch (e) {
    console.warn('error-shared/utils/drawRoute', { error: e });
    return false;
  }
};
export const clearDrawnRoute = (map) => {
  if (!map) return;
  map.routeController.clearAllRoutes();
};

export const searchLocations = async (map, search = '') => {
  if (!map || !search) return [];
  const data = await new Promise((resolve) => {
    map.searchForLocations(search, (matches) => {
      resolve(matches);
    });
  });
  return data;
};
export const searchCategories = async (map, search = '') => {
  if (!map || !search) return [];
  const data = await new Promise((resolve) => {
    map.searchForCategories(search, (matches) => {
      resolve(matches);
    });
  });
  return data;
};
export const groupConsecutiveDays = (data) => {
  const result = [];
  let tempGroup = [data[0]]; // Start with the first day

  for (let i = 1; i < data.length; i++) {
    const prev = data[i - 1];
    const current = data[i];

    // Check if the current day has the same hours as the previous day
    if (prev.opens === current.opens && prev.closes === current.closes) {
      tempGroup.push(current);
    } else {
      // Push the grouped days (or single day) to the result and start a new group
      result.push(tempGroup.length > 1 ? tempGroup : tempGroup[0]);
      tempGroup = [current];
    }
  }

  // Push the last group to the result
  result.push(tempGroup.length > 1 ? tempGroup : tempGroup[0]);

  return result;
};
export const debounce = (func, delay) => {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

export const groupByType = (data) => {
  const result = {};

  // Iterate over each item in the array
  data.forEach((item) => {
    const { type, node } = item;

    // Handle origin and destination types
    if (type === 'origin' || type === 'destination') {
      if (!node) {
        result[type] = null; // If no node, set value to null
      } else {
        result[type] = node; // Otherwise, set the full item
      }
    }

    // Handle middle type: group all items with type "middle" that have a node
    else if (type === 'middle') {
      if (item.node) {
        if (!result.middle) {
          result.middle = [];
        }
        result.middle.push(item.node); // Add item to middle array
      }
    }
  });

  return result;
};

export const calculateWalkingTime = (distanceInMeters) => {
  // Average walking speed in meters per second
  const averageSpeedMetersPerSecond = 1.4;

  // Calculate total time in seconds
  const totalTimeInSeconds = distanceInMeters / averageSpeedMetersPerSecond;

  // Calculate hours and minutes
  const hours = Math.floor(totalTimeInSeconds / 3600);
  const minutes = Math.ceil((totalTimeInSeconds % 3600) / 60);

  // Format the output
  if (hours > 0) {
    return `${hours} hours ${minutes} minutes`;
  } else {
    return `${minutes} minutes`;
  }
};

export const clearUrlParams = () => {
  const url = new URL(window.location);
  const org = url.searchParams.get('org'); // Get the 'ord' parameter

  // Clear the search parameters
  url.search = '';
  // Set only the 'ord' parameter if it exists
  if (org !== null) {
    url.searchParams.set('org', org);
  }
  // Update the URL without reloading the page
  window.history.pushState({}, '', url);
};

export const updateUrl = (newParams) => {
  const url = new URL(window.location);

  // Update the search parameters
  Object.keys(newParams).forEach((key) => {
    if (newParams[key]) {
      url.searchParams.set(key, newParams[key]);
    } else {
      url.searchParams.delete(key);
    }
  });

  // Update the URL without reloading the page
  window.history.pushState({}, '', url);
};
export const processSegments = (locations) => {
  // Check if locations array has enough elements
  if (locations.length < 2) return {};

  // Process locations into segments
  const segments = {};
  for (let i = 0; i < locations.length - 1; i++) {
    segments[i] = {
      from: locations[i],
      to: locations[i + 1],
    };
  }

  return segments;
};
export const isLessThanOrEqual = (value1, value2) => {
  // Split both values into major and minor parts
  const [major1, minor1] = value1.split('-').map(Number);
  const [major2, minor2] = value2.split('-').map(Number);

  // Compare major parts first; if equal, compare minor parts
  if (major1 < major2) {
    return true;
  } else if (major1 === major2) {
    return minor1 <= minor2;
  }
  return false;
};
export const directionImage = ({ direction, reference, referenceLocation, ...rest }) => {
  // console.log("directionImage-", { direction, reference, referenceLocation, ...rest });
  if (reference === 'At') {
    direction = 'HERE';
  }

  switch (direction) {
    case 'Left':
      return '/left.png';
    case 'SlightLeft':
      return '/slight-left.png';
    case 'Right':
      return '/right.png';
    case 'SlightRight':
      return '/slight-right.png';
    case 'Straight':
      return '/straight.png';
    case 'HERE':
    case 'Up':
    case 'Down': {
      const { amenity } = referenceLocation || {};
      switch (amenity) {
        case 'ELEVATOR':
          return '/lift.png';
        case 'ESCALATOR':
          return '/escalator.png';
        default:
          return '/default-direction.png';
      }
    }
    default:
      return '/gps.png';
  }
};

export const directionDetails = (
  { direction, reference, referenceLocation, action, ...rest },
  index = 0,
  steps = [],
  { from, to }
) => {
  // console.log("directionDetails-", { direction, reference, referenceLocation, action, ...rest }, index, steps, { from, to });
  const previousStep = steps[index - 1];
  const upcommingStep = steps[index + 1];
  // console.log(index, direction, { previousStep, upcommingStep, current: { direction, reference, referenceLocation, action, ...rest } });
  const defaultMessage = `Continue straight.`;
  if (action === 'Departure') {
    if (previousStep?.action === 'SwitchFloor') {
      return referenceLocation
        ? `You will arrive at ${referenceLocation.floor.shortName}.`
        : `You have arrived at your floor.`;
    }
    // return `Start your journey from here.`;
  }
  if (action === 'ArrivalDestination' && direction === 'None') {
    return `You have arrived at your destination.`;
  }
  switch (direction) {
    case 'Left':
      return `Turn left and follow the path.`;
    case 'SlightLeft':
      return `Make a slight left and follow the path.`;
    case 'Right':
      if (!previousStep) {
        return `Leave ${from.label} and turn right.`;
      }
      return `Turn right and follow the path.`;
    case 'SlightRight':
      return `Make a slight right and follow the path.`;
    case 'Straight':
      return defaultMessage;
    case 'Up':
    case 'Down': {
      const amenity = (referenceLocation?.amenity || '')?.toLowerCase();
      return referenceLocation
        ? `Take ${amenity} ${direction} ${upcommingStep ? `to ${upcommingStep.referenceLocation.floor.shortName}` : `from ${referenceLocation.floor.shortName}`} .`
        : `Take the lift and go ${direction.toLoercase()}.`;
    }
    // case 'Down':
    //     return liftMessage;
    default:
      return referenceLocation ? `You have arrived at floor ${referenceLocation.floor.shortName}.` : defaultMessage;
  }
};

export const limitedDirectionContent = ({ action, direction, reference, referenceLocation, prevStep, ...rest }) => {
  switch (action) {
    case 'Departure':
      if (['SwitchFloor'].includes(prevStep?.action)) {
        const amenity = (referenceLocation?.amenity || '')?.toLowerCase();
        return `Arrive at ${referenceLocation?.floor?.shortName} ${amenity}`; //TODO -- there is an issue when switched to different floor
        // return `Take the ${amenity} from ${prevStep?.referenceLocation?.floor?.shortName}.`;
      }
      if (['Departure'].includes(prevStep?.action)) {
        return `Reached ${prevStep?.referenceLocation?.floor?.shortName}`;
      }
      return `Start here`;
    case 'SwitchFloor':
      return `Take the ${referenceLocation?.amenity.toLowerCase()} from ${referenceLocation?.floor?.shortName}.`;
    // return direction === "Up" ? `Take the elevator up` : `Take the elevator down`;
    case 'ArrivalDestination':
      return `Arrive at destination`;
    default:
      switch (direction) {
        case 'Left':
          return `Turn left`;
        case 'SlightLeft':
          return `Turn Slight left`;
        case 'Right':
          return `Turn right`;
        case 'SlightRight':
          return `Turn Slight right`;
        case 'Straight':
          return `Go straight`;
        case 'Up':
          return `Elevator up`;
        case 'Down':
          return `Elevator down`;
        default:
          return `Arrived Destination`;
      }
  }
};

export const copyURLToClipboard = (url = '') => {
  // Get the current page's URL
  const currentURL = window.location.href;

  // Copy the URL to the clipboard
  navigator.clipboard
    .writeText(url || currentURL)
    .then(() => {
      toast.info('URL copied to clipboard!');
    })
    .catch((err) => {
      console.error('Failed to copy URL: ', err);
    });
};

const formatTime = (time) => moment(time, 'HH:mm').format('h:mm A');

export const processOperationHours = (location) => {
  const { operationHours: hoursData } = location || {};
  const dayOrder = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  let fullList = [];
  (hoursData || []).forEach((group) => {
    group.dayOfWeek.forEach((it) => {
      fullList.push({ day: it, opens: group.opens, closes: group.closes });
    });
  });
  dayOrder.forEach((day) => {
    if (!fullList.find((it) => it.day === day)) {
      fullList.push({ day, opens: null, closes: null });
    }
  });
  fullList = fullList.map((it) => {
    //find is the day is today and add a flag in to it using moment
    return { ...it, today: moment().format('dddd') == it.day };
  });

  // setFullList(fullList);
  //sort the list by day and group consecutive days (if any)
  const sortedData = fullList.length
    ? groupConsecutiveDays(fullList.sort((a, b) => dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day)))
    : [];

  const finalListData = sortedData
    .map((it) => {
      if (Array.isArray(it)) {
        let day = `${it[0].day.slice(0, 3)} - ${it[it.length - 1].day.slice(0, 3)}`;
        return { ...it[0], day };
      } else {
        return { ...it, day: it.day.slice(0, 3) };
      }
    })
    .map((it) => ({
      ...it,
      opens: it.opens ? formatTime(it.opens) : '',
      closes: it.closes ? formatTime(it.closes) : '',
    }));
  const todaysData = fullList.find((it) => it.today);

  return { finalList: finalListData, todaysData };
};

export const formatNumber = (phoneNumber = '') => {
  if (!phoneNumber) return '';
  const parsedNumber = parsePhoneNumberFromString('+' + phoneNumber);
  return parsedNumber ? parsedNumber.formatInternational() : 'Invalid phone number';
};

export const canUpdateArray = (array, showToast = true) => {
  for (let i = 0; i < array.length - 1; i++) {
    if (array[i].label === array[i + 1].label) {
      showToast && toast.info('Consecutive duplicate stops are not allowed.');
      return false; // Not allowed
    }
  }
  return true; // Allowed
};

/**
 * Fetch JSON data from a URL.
 * @param {string} url - The URL to fetch the data from.
 * @returns {Promise<Object>} - A Promise that resolves to the JSON data from the URL.
 */
export const fetchJsonData = async (url) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw new Error('Failed to fetch data: ' + error.message);
  }
};
export const convertTime = (time24) => {
  return moment(time24, 'HH:mm').format('hh:mm A');  // Convert 24-hour time to 12-hour format
}
export const formatString = (input) => {
  return input
    .toLowerCase() // Convert to lowercase
    .split('_') // Split by underscore
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
    .join(' '); // Join words with a space
};

export async function captureUserDetails(data={}) {
  const myHeaders = new Headers();
  myHeaders.append("beco-usertoken", "541cbd26c4d812644eb493d16cc8106b266fc2c7");
  myHeaders.append("Content-Type", "application/json");

  const raw = JSON.stringify({
    ...data,
    // "timestamp": moment().format()  // Current local time using moment
  });

  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: raw,
    redirect: "follow"
  };

  const response = await fetch("https://app.becomap.com/api/v1/careroutes/user-capture/", requestOptions);
  const result = await response.text();
  return result;
}

export const removeDuplicates = (arr) => {
  const uniqueMap = new Map();
  return arr.filter(item => !uniqueMap.has(item.id) && uniqueMap.set(item.id, true));
};
export const metersToFeet = (meters) => {
  const feetPerMeter = 3.28084;
  return meters * feetPerMeter;
}

export const analytics = import.meta.env.VITE_APP_GOOGLE_ANALYTICS_ID
  ? Analytics({
      app: 'hp-events',
      plugins: [
        googleAnalytics({
          measurementIds: [import.meta.env.VITE_APP_GOOGLE_ANALYTICS_ID || ''],
        }),
      ],
    })
  : null;