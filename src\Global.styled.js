import {
	createGlobalStyle
}

from 'styled-components';

const GlobalStyle=createGlobalStyle` *,
*::before,
*::after {
	box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
	:root {
		scroll-behavior: smooth;
	}
}

html {
	font-size: 16px;
	-ms-touch-action: none;
	touch-action: none;
	-ms-content-zooming: none;
	overflow: hidden;
}

body,
html {
	width: 100%;
	height: 100%;
	-ms-touch-action: none;
	touch-action: none;
	overscroll-behavior: none;
	/* overscroll-behavior-y: contain; */
	position: relative;
	overflow: hidden;


}

body {
	margin: 0;
	font-family: "Inter", sans-serif;
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #0f172a;
	text-align: start;
	background-color: #ffffff;
	-webkit-text-size-adjust: 100%;
	-webkit-tap-highlight-color: transparent;
	user-select: none;

	.introjs-tooltip-header {
		position: relative;
		padding-left: 10px;
		padding-right: 20px;
		padding-top: 4px;
		min-height: 1.5em;
	}

	.introjs-tooltiptext {
		padding: 5px 15px;
		margin-bottom: 0;
		font: 14px / normal sans-serif;
		line-height: 1.5;
		font-weight: 400;
		zoom: 1;
	}

	.introjs-skipbutton {
		all: unset;
		position: absolute;
		top: 0;
		right: 0;
		width: 32px;
		height: 28px;
		cursor: pointer;
		font-size: 18px;
		font-weight: 500;
		color: #d2d2d2;
		z-index: 1;
		text-align: center;
		transition: color;
		transition-duration: .2s;
		color: #2d2d2d;
	}

	.introjs-bullets {
		text-align: center;
		padding-top: 0;
		padding-bottom: 0;
	}

	.introjs-tooltipbuttons {
		text-align: right;
		zoom: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		display: flex;
		justify-content: flex-end;
		padding: 10px;
	}

	.introjs-button {
		all: unset;
		display: inline-block;
		box-sizing: border-box;
		padding: 3px 7px;
		text-decoration: none;
		text-shadow: 1px 1px 0 #fff;
		background-color: #fff;
		color: #2d2d2d;
		font: 12px / normal sans-serif;
		cursor: pointer;
		outline: 0;
		zoom: 1;
		line-height: 1.3;
		border: 1px solid #ccc;
		border-radius: 3px;
		transition: all 0.2s;
		text-shadow: none;
		border: none;
		line-height: 1.5;
	}

	.introjs-prevbutton {
		color: #000;
		background-color: transparent;
		border: 1px solid #cdcdcd;
	}

	.introjs-nextbutton {
		background-color: rgb(52, 168, 83);
		color: white;
		border: 1px solid rgb(52, 168, 83);
		margin-left: 5px;

		&:hover,
		&:focus {
			background-color: #2b8a4a;


			outline: 0;
			text-decoration: none;
			box-shadow: none;
			border: 1px solid rgb(52, 168, 83);
			color: #fff;
		}
	}
	.introjs-tooltipReferenceLayer {
		visibility: visible;
	}
}

#root {
	display: flex;
	flex-direction: column;
	height: 100%;
	overflow: hidden;

}

input,
button,
select,
optgroup,
textarea {
	margin: 0;
	font-family: inherit;
	font-size: inherit;
	line-height: inherit;
}


button {
	user-select: none;
	appearance: none;
	border: none;
	background: none;
}

input {
	box-shadow: none;
	outline: none;
}

.sortable-ghost {
	opacity: 0.3;
	background-color: transparent;
	border: 1px dashed ${({ theme })=>theme.colors.primary
}

;
}

.sortable-chosen {
	opacity: 0.8;
	transform: scale(0.98);
}

div {
	scrollbar-width: thin;
	scrollbar-color: #d5d5d5 #ffffff00;
}

.leaflet-bottom {
	bottom: 0;
}

.leaflet-right {
	right: 0;
}

.leaflet-bottom,
.leaflet-top {
	position: absolute;
	z-index: 1000;
	pointer-events: none;
}


.leaflet-control {
	position: relative;
	z-index: 800;
	pointer-events: visiblePainted;
	pointer-events: auto;
}

.leaflet-control {
	float: left;
	clear: both;
}

.leaflet-control,
.leaflet-popup-pane {
	cursor: auto;
}


.leaflet-right .leaflet-control {
	float: right;
}

.leaflet-bottom .leaflet-control {
	margin-bottom: 10px;
}

.leaflet-right .leaflet-control {
	margin-right: 10px;
}

.leaflet-right .leaflet-control {
	margin-right: 15px;
}

.leaflet-right .leaflet-control {
	margin-right: 15px;
}

.leaflet-control.leaflet-control-zoom {
	margin-bottom: 20px;
	border: 0;
	box-shadow: 0 0 4px 0 rgba(0, 0, 0, .3), 0 0 10px 0 rgba(0, 0, 0, .3);
	background: #fff;
	border-radius: 20px;
	overflow: hidden;
}


.leaflet-touch .leaflet-bar .leaflet-control-zoom-btn {
	width: 30px;
	height: 30px;
	line-height: 30px;
}


.leaflet-control.leaflet-control-zoom .leaflet-control-zoom-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 0 4px;
	border: 0;
	color: #4c4c4c;
	height: 36px;
	width: 40px;
	font-size: 24px;
}

.leaflet-control.leaflet-control-zoom .leaflet-control-zoom-btn {
	height: 36px;
}

.leaflet-touch .leaflet-bar .leaflet-control-zoom-btn:first-child {
	border-top-left-radius: 2px;
	border-top-right-radius: 2px;
}

.leaflet-control.leaflet-control-zoom .leaflet-control-zoom-btn:first-child {
	padding: 4px 0 0;
}

.beco-switcher {
	margin-bottom: 8px;
	pointer-events: all;
}

@media (max-width: 575.98px) {
	.beco-switcher {
		margin-bottom: 10px;
	}
}

.beco-switcher .floor-dropdown {
	position: relative;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	vertical-align: middle;
}

.beco-switcher .floor-dropdown button {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	align-items: center;
	justify-content: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	padding: 8px 3px;
	font-size: 14px;
	border-radius: 5px;
	position: relative;
	background: #fff;
	width: 42px;
	min-height: 42px;
	cursor: pointer;
	border: 0;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 -1px 0px rgba(0, 0, 0, 0.02);
	color: #000;
	font-weight: 600;
}

.beco-switcher .floor-dropdown button:after {
	display: inline-block;
	width: 0;
	height: 0;
	margin-left: 4px;
	content: "";
	border-top: 0;
	border-right: 0.3em solid transparent;
	border-bottom: 0.3em solid;
	border-left: 0.3em solid transparent;
}

.beco-switcher .floor-dropdown button.show {
	box-shadow: none;
	border-top-left-radius: 0px;
	border-top-right-radius: 0px;
}

.beco-switcher .floor-dropdown button.show:after {
	border-bottom: 0;
	border-top: 0.3em solid;
}

@media (max-width: 575.98px) {
	.beco-switcher .floor-dropdown button {
		padding: 8px 3px;
		font-size: 16px;
		border-radius: 5px;
		width: 50px;
		min-height: 50px;
	}
}

.beco-switcher .floor-dropdown ul {
	border-radius: 20px;
	box-shadow: 0 0.156rem 0.156rem #00194b0d, 0 0 0.5rem #00194b0d,
		0 0.313rem 0.313rem #00194b0d;
	overflow: hidden;
	background-color: #fff;
	will-change: transform;
	width: 40px;
	padding: 0;
	min-width: inherit;
	margin: 0;
	border-bottom: none;
}

@media (max-width: 575.98px) {
	.beco-switcher .floor-dropdown ul {
		width: 40px;
		border-radius: 25px;
	}
}

.beco-switcher .floor-dropdown ul li {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	align-items: center;
	justify-content: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	text-align: center;
	width: 100%;
	clear: both;
	font-weight: 500;
	color: #212529;
	text-align: inherit;
	white-space: nowrap;
	background-color: transparent;
	border: 0;
	padding: 8px 3px;
	color: #2e2e2e;
	font-size: 14px;
	cursor: pointer;
}

.beco-switcher .floor-dropdown ul li:hover {
	background-color: #eee;
}

.beco-switcher .floor-dropdown ul li.disabled {
	color: #ababab;
	pointer-events: none;
	background-color: #e4e4e4;
}

.beco-switcher .floor-dropdown ul li+li {
	border-top: 1px solid #e9ecef;
}

.beco-switcher .floor-dropdown ul li.active {
	background-color: #1da1f2;
	color: #fff;
	border-top: 1px solid transparent;
}

.beco-switcher .floor-dropdown ul li.active:hover {
	background-color: #1da1f2;
	color: #fff;
}

@media (max-width: 575.98px) {
	.beco-switcher .floor-dropdown ul li {
		font-size: 14px;
	}
}

html[dir="rtl"] .beco-switcher {
	position: absolute;
	top: 0;
	right: auto;
	left: 10px;
}

html[dir="rtl"] .beco-switcher .floor-dropdown button:after {
	margin-left: 0;
	margin-right: 4px;
}

canvas {
	outline: none;
}

.svg-active,
.svg-inactive {
	transition: transform 0.3s ease, opacity 0.3s ease;
}

.svg-active {
	transform: scale(1);
}

.svg-inactive {
	transform: scale(.9);
}

`;

export default GlobalStyle;