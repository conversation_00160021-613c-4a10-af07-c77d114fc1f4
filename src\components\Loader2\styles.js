import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

export const LoaderOverlay = styled.div`
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${(props) => (props.$visible ? 1 : 0)};
  visibility: ${(props) => (props.$visible ? 'visible' : 'hidden')};
  transition:
    opacity 0.2s,
    visibility 0.2s;
  background-color: #f7f7f7;
`;

export const LoaderContainer = styled.div`
  padding: 1rem;
  border-radius: 0.5rem;

  .loader {
    width: 80px;
    aspect-ratio: 1;
    padding: 10px;
    box-sizing: border-box;
    display: grid;
    background: #fff;
    filter: blur(5px) contrast(20) hue-rotate(317deg);
    mix-blend-mode: darken;
  }
  .loader:before,
  .loader:after {
    content: '';
    grid-area: 1/1;
    width: 26px;
    height: 26px;
    background: rgb(23, 143, 191);
    animation: l7 2s infinite;
  }
  .loader:after {
    animation-delay: -1s;
  }
  @keyframes l7 {
    0% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(100%, 0);
    }
    50% {
      transform: translate(100%, 100%);
    }
    75% {
      transform: translate(0, 100%);
    }
    100% {
      transform: translate(0, 0);
    }
  }
`;
