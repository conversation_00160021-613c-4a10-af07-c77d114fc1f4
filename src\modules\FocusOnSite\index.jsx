import { useMap } from '../../context/MapContext';
import { FocusControl } from '../MapController/index.styled';
import Icon from '@/components/shared/Icon';

const FocusOnMap = () => {
  const { resetToDefaultViewport,isStarted } = useMap();
  
  if (isStarted) return <></>;
  return (
    <FocusControl>
      <button onClick={() => resetToDefaultViewport()} title="Focus" aria-label="Focus">
        <Icon icon={'mark'} size={'22'} />
      </button>
    </FocusControl>
  );
};

export default FocusOnMap;
