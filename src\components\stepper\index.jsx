import { QuestionNairProgress, QuestionNairProgressIndicator } from '../../modules/MapView/index.styled';

// export default Stepper;
const Stepper = ({ step, totalQuestions }) => {
  return (
    <QuestionNairProgress>
      {[...Array(totalQuestions)].map((_, i) => (
          <QuestionNairProgressIndicator key={i + 1} className={`${i + 1 <= step ? 'stepper-cell-filled' : ''}`}/>
      ))}
    </QuestionNairProgress>

    // <div className="stepper-wrap">
    //   <h3>
    //     QUESTION {step} OF {totalQuestions}
    //   </h3>
    //   <div className="stepper-row d-flex">
    //     {[...Array(totalQuestions)].map((_, i) => (
    //       <div key={i + 1} className={`steper-cell ${i + 1 <= step ? 'stepper-cell-filled' : ''}`}></div>
    //     ))}
    //   </div>
    // </div>
  );
};

export default Stepper;
