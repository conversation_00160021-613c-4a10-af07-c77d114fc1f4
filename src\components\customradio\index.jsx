import React from 'react';
import { SingleAnswer, SingleAnswerInput } from '../../modules/MapView/index.styled';

const CustomRadioButton = ({ id, label, checked, onChange }) => {
  return (
    // <div className={`radio-box ${checked ? 'selected' : ''}`} onClick={() => onChange(id)}>
    //   <Form.Check
    //     type="radio"
    //     id={id}
    //     name="radio-group"
    //     checked={checked}
    //     onChange={() => onChange(id)}
    //     style={{ display: 'none' }}
    //   />
    //   <div className="custom-radio">
    //     {checked && (
    //       <span className="checkmark">
    //         <img src={TickImg} alt="tick" />
    //       </span>
    //     )}
    //   </div>
    //   <label htmlFor={id} className="radio-label">
    //     {label}
    //   </label>
    // </div>
    <SingleAnswer htmlFor={id} onClick={() => onChange(id)}>
      <SingleAnswerInput
       isRadio
        type="radio"
        id={id}
        name="radio-group"
        // value={option}
        checked={checked}
        onChange={() => onChange(id)}
      />
      {label}
    </SingleAnswer>
  );
};

export default CustomRadioButton;
