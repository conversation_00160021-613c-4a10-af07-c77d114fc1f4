import styled, { keyframes } from 'styled-components';


const LoaderWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 999;
  opacity: ${(props) => (props.$visible ? 1 : 0)};
  visibility: ${(props) => (props.$visible ? 'visible' : 'hidden')};
  span {
    background-image: url(/becomap-loader.gif);
    width: 80px;
    height: 80px;
    position: absolute;
    left: 50%;
    top: 50%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transform: translate(-50%, -50%);
  }
`;

export const Loader = ({ loading }) => {
  if (!loading) return null;

  return (
    <LoaderWrapper $visible={loading}>
      <span></span>
    </LoaderWrapper>
  );
};
